server:
  host: "127.0.0.1"  # 本地开发环境
  port: 8080
  mode: "debug"
  timeout: 30

database:
  driver: mysql
  host: localhost
  port: 3306
  username: root
  password: "<PERSON><PERSON><PERSON>&1990!!"  # 通过环境变量 DB_PASSWORD 注入
  dbname: print_ease
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

auth:
  jwt:
    secret_key: "xP8bk#mK9$vN2^pL5@jR7*qW4nY6&tH3"  # 建议使用环境变量注入
    expiration_times:
      merchant:
        client: 168   # 客户端7天（预留）
        web: 24       # 商家网页1天（预留）
        mp: 168       # 公众号7天（预留）
        agent: 168    # 代理程序7天

    refresh_times:
      merchant:
        client: 160   # 客户端6.7天（预留）
        web: 20       # 商家网页20小时（预留）
        mp: 160       # 公众号6.7天（预留）
        agent: 160    # 代理程序6.7天

store:
  maxDistance: 20.0  # 最大服务范围,单位:公里 

upload:
  uploadDir: "uploads"
  maxFileSize: 32  # 32MB