package main

import (
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/middleware"
	"PrintEaseBackend/internal/routes"
	"fmt"
	"log"
	"os"

	"github.com/gin-gonic/gin"
)

func main() {
	// 打印当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		log.Printf("获取当前目录失败: %v", err)
	} else {
		log.Printf("当前工作目录: %s", currentDir)
	}

	// 加载配置
	if err := config.LoadConfig(); err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 确保上传目录存在
	if err := os.MkdirAll(config.GlobalConfig.Upload.UploadDir, 0755); err != nil {
		log.Printf("创建上传目录失败: %v", err)
	}

	// 初始化数据库连接
	err = database.InitDB(&config.GlobalConfig.Database)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}
	log.Println("数据库连接成功")

	// 设置gin模式
	gin.SetMode(config.GlobalConfig.Server.Mode)

	// 创建gin实例
	r := gin.Default()

	// 设置文件上传限制
	r.MaxMultipartMemory = int64(config.GlobalConfig.Upload.MaxFileSize) << 20 // 转换为MB

	// 全局中间件
	r.Use(middleware.Logger()) // 日志记录
	r.Use(middleware.Cors())   // CORS处理

	// 注册路由
	routes.InitRoutes(r)

	// 启动服务
	serverAddr := fmt.Sprintf("%s:%d",
		config.GlobalConfig.Server.Host,
		config.GlobalConfig.Server.Port,
	)
	if err := r.Run(serverAddr); err != nil {
		log.Fatalf("启动服务失败: %v", err)
	}
}
