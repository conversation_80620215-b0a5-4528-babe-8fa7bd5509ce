import sys
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QLabel, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt
import win32print

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("PyQt5和打印功能测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        self.label = QLabel("PyQt5安装成功！")
        self.label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label)
        
        # 添加打印机测试按钮
        self.print_button = QPushButton("测试打印机列表")
        self.print_button.clicked.connect(self.test_printers)
        layout.addWidget(self.print_button)
        
        # 添加打印机信息标签
        self.printer_label = QLabel("点击按钮查看打印机")
        self.printer_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.printer_label)
    
    def test_printers(self):
        try:
            # 获取打印机列表
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 1)]
            default_printer = win32print.GetDefaultPrinter()
            
            # 显示打印机信息
            printer_info = f"默认打印机: {default_printer}\n\n可用打印机:\n"
            printer_info += "\n".join(printers)
            
            self.printer_label.setText(printer_info)
        except Exception as e:
            self.printer_label.setText(f"获取打印机失败: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())