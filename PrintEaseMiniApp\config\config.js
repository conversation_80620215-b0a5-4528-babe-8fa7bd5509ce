// 小程序环境中没有 process 对象，使用固定环境
const env = 'development';

// 环境相关配置
const envConfig = {
	development: {
		baseUrl: 'http://127.0.0.1:8080'
	},
	production: {
		baseUrl: 'https://your-api-domain.com'
	}
};

// 获取当前环境的baseUrl
const baseUrl = envConfig[env].baseUrl;

// 全局配置
const config = {
	// API相关配置
	api: {
		baseUrl: baseUrl, // 从环境配置中获取
		timeout: 10000, // 请求超时时间(ms)
	},
	
	// 文件上传相关配置
	upload: {
		maxCount: 9, // 最大上传文件数量
		maxSize: 20 * 1024 * 1024, // 单个文件最大大小(20MB)
		allowedFileTypes: ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf', 'txt', 'rtf'], // 允许的文档类型
		allowedImageTypes: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tif', 'tiff'] // 允许的图片类型
	},
	
	// 缓存相关配置
	cache: {
		fileExpireTime: 7 * 24 * 60 * 60 * 1000, // 文件缓存过期时间(7天)
	},
};

// 导出完整配置
export default config; 