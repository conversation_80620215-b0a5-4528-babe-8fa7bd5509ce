import config from '../config/config.js'

// 创建请求对象
const request = {
	config: {
		baseUrl: config.baseUrl || 'http://127.0.0.1:8080'
	},
	
	// GET请求
	get(url, data = {}) {
		return this.request('GET', url, data)
	},
	
	// POST请求
	post(url, data = {}) {
		return this.request('POST', url, data)
	},
	
	// PUT请求
	put(url, data = {}) {
		return this.request('PUT', url, data)
	},
	
	// DELETE请求
	delete(url, data = {}) {
		return this.request('DELETE', url, data)
	},
	
	// 通用请求方法
	request(method, url, data = {}) {
		return new Promise((resolve, reject) => {
			console.log(`发起${method}请求: ${this.config.baseUrl}${url}`, data)
			uni.request({
				url: this.config.baseUrl + url,
				method: method,
				data: data,
				header: {
					'Content-Type': 'application/json',
					'token': uni.getStorageSync('token') || ''
				},
				success: (res) => {
					console.log(`请求成功: ${url}`, res.data)
					resolve(res.data)
				},
				fail: (err) => {
					console.error(`请求失败: ${url}`, err)
					reject(err)
				}
			})
		})
	}
}

export default request 