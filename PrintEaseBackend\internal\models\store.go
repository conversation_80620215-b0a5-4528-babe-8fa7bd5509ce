package models

import (
	"time"
)

// Store 商家模型
type Store struct {
	// 基础信息
	ID        uint64  `gorm:"primarykey"`
	Name      string  `gorm:"type:varchar(100);not null" json:"name"`
	Phone     string  `gorm:"size:20;not null" json:"phone"`
	Address   string  `gorm:"type:varchar(255)" json:"address"`
	Latitude  float64 `gorm:"type:decimal(10,7);index:idx_location" json:"latitude"`
	Longitude float64 `gorm:"type:decimal(10,7);index:idx_location" json:"longitude"`

	// 营业信息
	BusinessHours string `gorm:"size:100;not null" json:"business_hours"`
	Announcement  string `gorm:"type:text" json:"announcement"`
	// 0：open，营业中， 1：closed，已打烊，2：likely_shutdown，疑似关闭, 3：shutdown，已关闭
	Status string `json:"status"`

	// 管理信息
	AdminID   uint64     `gorm:"not null" json:"admin_id"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `gorm:"index" json:"deleted_at"`

	// 计算字段
	Distance float64 `gorm:"-" json:"distance"` // 用于存储计算出的距离
}

// TableName 指定表名
func (Store) TableName() string {
	return "stores"
}
