import os
import time
import logging
import win32print
import win32api
import win32con
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class PrintManager:
    """打印管理器类，负责与系统打印机交互"""

    def __init__(self):
        """初始化打印管理器"""
        self.default_printer = win32print.GetDefaultPrinter()
        logger.info(f"初始化打印管理器，默认打印机: {self.default_printer}")

    def get_all_printers(self) -> List[str]:
        """获取系统中所有打印机的列表"""
        try:
            # PRINTER_ENUM_LOCAL = 2 表示枚举本地打印机
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 1)]
            logger.info(f"获取到 {len(printers)} 台打印机")
            return printers
        except Exception as e:
            logger.error(f"获取打印机列表失败: {str(e)}")
            return []

    def get_default_printer(self) -> str:
        """获取默认打印机名称"""
        try:
            return win32print.GetDefaultPrinter()
        except Exception as e:
            logger.error(f"获取默认打印机失败: {str(e)}")
            return ""

    def set_default_printer(self, printer_name: str) -> bool:
        """设置默认打印机"""
        try:
            # 检查打印机是否存在
            printers = self.get_all_printers()
            if printer_name not in printers:
                logger.error(f"打印机 '{printer_name}' 不存在")
                return False

            # 设置默认打印机
            win32print.SetDefaultPrinter(printer_name)
            logger.info(f"已将 '{printer_name}' 设置为默认打印机")
            return True
        except Exception as e:
            logger.error(f"设置默认打印机失败: {str(e)}")
            return False

    def get_printer_status(self, printer_name: str) -> Dict[str, Any]:
        """获取打印机状态"""
        try:
            # 打开打印机
            h_printer = win32print.OpenPrinter(printer_name)

            # 获取打印机信息
            printer_info = win32print.GetPrinter(h_printer, 2)

            # 关闭打印机
            win32print.ClosePrinter(h_printer)

            # 解析状态
            status = printer_info["Status"]
            status_info = {
                "status_code": status,
                "status_text": self._get_status_text(status),
                "is_online": (status == 0),  # 0 表示正常
                "is_error": (status & win32print.PRINTER_STATUS_ERROR) != 0,
                "is_paper_jam": (status & win32print.PRINTER_STATUS_PAPER_JAM) != 0,
                "is_paper_out": (status & win32print.PRINTER_STATUS_PAPER_OUT) != 0,
                "is_printing": (status & win32print.PRINTER_STATUS_PRINTING) != 0,
                "is_offline": (status & win32print.PRINTER_STATUS_OFFLINE) != 0,
            }

            logger.info(f"打印机 '{printer_name}' 状态: {status_info['status_text']}")
            return status_info
        except Exception as e:
            logger.error(f"获取打印机 '{printer_name}' 状态失败: {str(e)}")
            return {
                "status_code": -1,
                "status_text": f"获取状态失败: {str(e)}",
                "is_online": False,
                "is_error": True,
                "is_paper_jam": False,
                "is_paper_out": False,
                "is_printing": False,
                "is_offline": True,
            }

    def _get_status_text(self, status: int) -> str:
        """根据状态码返回状态文本"""
        if status == 0:
            return "正常"

        status_texts = []

        if status & win32print.PRINTER_STATUS_PAUSED:
            status_texts.append("已暂停")
        if status & win32print.PRINTER_STATUS_ERROR:
            status_texts.append("错误")
        if status & win32print.PRINTER_STATUS_PENDING_DELETION:
            status_texts.append("正在删除")
        if status & win32print.PRINTER_STATUS_PAPER_JAM:
            status_texts.append("卡纸")
        if status & win32print.PRINTER_STATUS_PAPER_OUT:
            status_texts.append("缺纸")
        if status & win32print.PRINTER_STATUS_MANUAL_FEED:
            status_texts.append("手动进纸")
        if status & win32print.PRINTER_STATUS_PAPER_PROBLEM:
            status_texts.append("纸张问题")
        if status & win32print.PRINTER_STATUS_OFFLINE:
            status_texts.append("离线")
        if status & win32print.PRINTER_STATUS_PRINTING:
            status_texts.append("打印中")

        if not status_texts:
            return f"未知状态({status})"

        return ", ".join(status_texts)

    def print_file(self, file_path: str, printer_name: Optional[str] = None, config: Optional[Dict[str, Any]] = None) -> bool:
        """打印文件

        Args:
            file_path: 要打印的文件路径
            printer_name: 打印机名称，如果为None则使用默认打印机
            config: 打印配置，如果为None则使用默认配置

        Returns:
            bool: 打印是否成功
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            # 如果未指定打印机，使用默认打印机
            if printer_name is None:
                printer_name = self.get_default_printer()

            # 检查打印机是否存在
            printers = self.get_all_printers()
            if printer_name not in printers:
                logger.error(f"打印机 '{printer_name}' 不存在")
                return False

            # 获取文件扩展名
            _, ext = os.path.splitext(file_path)
            ext = ext.lower()

            # 根据文件类型选择不同的打印方法
            if ext in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt', '.rtf']:
                # 使用ShellExecute打印文档
                return self._print_document(file_path, printer_name)
            elif ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff']:
                # 使用图像打印方法
                return self._print_image(file_path, printer_name, config)
            else:
                logger.error(f"不支持的文件类型: {ext}")
                return False

        except Exception as e:
            logger.error(f"打印文件失败: {str(e)}")
            return False

    def _print_document(self, file_path: str, printer_name: str) -> bool:
        """打印文档文件(PDF, WORD, EXCEL, PPT, TXT)"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return False

            # 检查文件是否可读
            if not os.access(file_path, os.R_OK):
                logger.error(f"文件无法读取: {file_path}")
                return False

            # 获取文件绝对路径
            abs_path = os.path.abspath(file_path)
            logger.info(f"打印文件的绝对路径: {abs_path}")

            # 保存当前默认打印机
            current_default = self.get_default_printer()
            logger.info(f"当前默认打印机: {current_default}")

            # 临时设置为指定的打印机
            success = self.set_default_printer(printer_name)
            if not success:
                logger.error(f"设置默认打印机失败: {printer_name}")
                return False

            logger.info(f"已将默认打印机设置为: {printer_name}")

            # 使用ShellExecute打印文件
            logger.info(f"开始使用ShellExecute打印文件: {abs_path}")
            ret = win32api.ShellExecute(
                0,
                "print",
                abs_path,
                None,
                ".",
                0
            )
            logger.info(f"ShellExecute返回值: {ret}")

            # 恢复默认打印机
            if current_default != printer_name:
                self.set_default_printer(current_default)
                logger.info(f"已恢复默认打印机: {current_default}")

            logger.info(f"已发送文件 '{abs_path}' 到打印机 '{printer_name}'")
            return True

        except Exception as e:
            logger.error(f"打印文档失败: {str(e)}")
            return False

    def _print_image(self, file_path: str, printer_name: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """打印图像文件

        注意：此方法需要安装PIL库
        """
        try:
            from PIL import Image, ImageWin
            import win32ui

            # 打开打印机
            h_printer = win32print.OpenPrinter(printer_name)
            printer_info = win32print.GetPrinter(h_printer, 2)

            # 创建设备上下文
            h_dc = win32ui.CreateDC()
            h_dc.CreatePrinterDC(printer_name)

            # 开始文档
            h_dc.StartDoc(os.path.basename(file_path))
            h_dc.StartPage()

            # 打开图像
            img = Image.open(file_path)

            # 获取打印机分辨率
            dpi_x = h_dc.GetDeviceCaps(win32con.LOGPIXELSX)
            dpi_y = h_dc.GetDeviceCaps(win32con.LOGPIXELSY)

            # 获取打印区域（以像素为单位）
            width_pixels = h_dc.GetDeviceCaps(win32con.PHYSICALWIDTH)
            height_pixels = h_dc.GetDeviceCaps(win32con.PHYSICALHEIGHT)

            # 计算图像尺寸
            img_width, img_height = img.size
            ratio = min(width_pixels / img_width, height_pixels / img_height)

            # 调整图像大小以适应打印区域
            new_width = int(img_width * ratio)
            new_height = int(img_height * ratio)

            # 计算居中位置
            x = (width_pixels - new_width) // 2
            y = (height_pixels - new_height) // 2

            # 打印图像
            dib = ImageWin.Dib(img)
            dib.draw(h_dc.GetHandleOutput(), (x, y, x + new_width, y + new_height))

            # 结束页面和文档
            h_dc.EndPage()
            h_dc.EndDoc()

            # 清理
            h_dc.DeleteDC()
            win32print.ClosePrinter(h_printer)

            logger.info(f"已打印图像 '{file_path}' 到打印机 '{printer_name}'")
            return True

        except ImportError:
            logger.error("打印图像需要安装PIL库，请使用 'pip install pillow' 安装")
            return False
        except Exception as e:
            logger.error(f"打印图像失败: {str(e)}")
            return False

    def print_test_page(self, printer_name: Optional[str] = None) -> bool:
        """打印测试页

        Args:
            printer_name: 打印机名称，如果为None则使用默认打印机

        Returns:
            bool: 打印是否成功
        """
        temp_path = None
        try:
            # 如果未指定打印机，使用默认打印机
            if printer_name is None:
                printer_name = self.get_default_printer()

            # 检查打印机是否存在
            printers = self.get_all_printers()
            if printer_name not in printers:
                logger.error(f"打印机 '{printer_name}' 不存在")
                return False

            # 使用程序自己的temp目录
            # 获取程序根目录
            root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            temp_dir = os.path.join(root_dir, 'temp')

            # 确保temp目录存在
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
                logger.info(f"创建临时目录: {temp_dir}")

            # 创建临时文件作为测试页
            timestamp = int(time.time())
            temp_file = f"test_page_{timestamp}.txt"
            temp_path = os.path.join(temp_dir, temp_file)

            logger.info(f"准备创建测试页临时文件: {temp_path}")

            # 写入测试页内容
            try:
                with open(temp_path, 'w', encoding='utf-8') as f:
                    f.write("PrintEase 打印测试页\n\n")
                    f.write("=" * 50 + "\n")
                    f.write("打印机名称: " + printer_name + "\n")
                    f.write("打印时间: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n")
                    f.write("时间戳: " + str(timestamp) + "\n")
                    f.write("这是一个测试页，用于测试打印机是否正常工作。\n")
                    f.write("如果您能看到这个页面，说明打印机工作正常。\n\n")
                    f.write("=" * 50 + "\n")
                logger.info(f"测试页内容已写入文件")
            except Exception as e:
                logger.error(f"写入测试页内容失败: {str(e)}")
                return False

            # 确认文件存在
            if not os.path.exists(temp_path):
                logger.error(f"临时文件创建失败，文件不存在: {temp_path}")
                return False

            # 获取文件大小
            file_size = os.path.getsize(temp_path)
            logger.info(f"临时文件创建成功，大小: {file_size} 字节")

            # 检查文件是否可读
            if not os.access(temp_path, os.R_OK):
                logger.error(f"临时文件无法读取: {temp_path}")
                return False

            # 打印测试页
            logger.info(f"开始打印测试页: {temp_path} -> {printer_name}")
            result = self._print_document(temp_path, printer_name)

            if result:
                logger.info(f"测试页打印成功: {printer_name}")
            else:
                logger.error(f"测试页打印失败: {printer_name}")

            return result

        except Exception as e:
            logger.error(f"打印测试页失败: {str(e)}")
            return False
#        finally:
#            # 删除临时文件
#            if temp_path and os.path.exists(temp_path):
#                try:
#                    os.remove(temp_path)
#                    logger.info(f"临时文件已删除: {temp_path}")
#                except Exception as e:
#                    logger.warning(f"删除临时文件失败: {temp_path}, 错误: {str(e)}")
                    