package handlers

import (
	"PrintEaseBackend/internal/auth"
	"PrintEaseBackend/internal/config"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var (
	upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许所有来源的WebSocket连接
		},
	}

	// 单例模式
	wsHandlerInstance *WebSocketHandler
	once              sync.Once
)

// WebSocketHandler 处理WebSocket连接
type WebSocketHandler struct {
	// 连接池，存储商家ID与连接的映射
	connections map[int64]*websocket.Conn
	// 互斥锁，保护连接池
	mutex sync.RWMutex
}

// NewWebSocketHandler 创建WebSocket处理器实例
func NewWebSocketHandler() *WebSocketHandler {
	once.Do(func() {
		wsHandlerInstance = &WebSocketHandler{
			connections: make(map[int64]*websocket.Conn),
		}
	})
	return wsHandlerInstance
}

// GetWebSocketHandler 获取WebSocket处理器实例
func GetWebSocketHandler() *WebSocketHandler {
	return NewWebSocketHandler()
}

// HandleConnection 处理WebSocket连接请求
func (h *WebSocketHandler) HandleConnection(c *gin.Context) {
	// 获取商家ID
	merchantIDStr := c.Query("merchant_id")
	if merchantIDStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "缺少商家ID参数",
		})
		return
	}

	// 解析商家ID
	merchantID, err := strconv.ParseInt(merchantIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的商家ID",
		})
		return
	}

	// 从Authorization头获取令牌
	authHeader := c.GetHeader("Authorization")
	if authHeader == "" {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  "缺少认证信息",
		})
		return
	}

	// 解析Bearer令牌
	parts := strings.Split(authHeader, " ")
	if len(parts) != 2 || parts[0] != string(auth.TokenTypeBearer) {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  "无效的认证格式",
		})
		return
	}
	tokenString := parts[1]

	// 验证令牌
	jwtManager := auth.NewJWTManager(config.GlobalConfig.Auth)
	claims, err := jwtManager.ValidateToken(tokenString)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  "无效的令牌",
			"err":  err.Error(),
		})
		return
	}

	// 验证客户端类型
	if claims.ClientType != auth.ClientTypeAgent {
		c.JSON(http.StatusForbidden, gin.H{
			"code": -1,
			"msg":  "非法的客户端类型",
		})
		return
	}

	// 验证商家ID是否匹配
	if claims.UserID != merchantID {
		c.JSON(http.StatusForbidden, gin.H{
			"code": -1,
			"msg":  "商家ID与令牌不匹配",
		})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "连接升级失败",
			"err":  err.Error(),
		})
		return
	}

	// 保存连接
	h.SaveConnection(merchantID, conn)

	log.Printf("代理程序已连接 [商家ID: %d]", merchantID)

	// 处理连接关闭
	defer func() {
		conn.Close()
		h.RemoveConnection(merchantID)
		log.Printf("代理程序已断开 [商家ID: %d]", merchantID)
	}()

	// 处理消息
	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("读取消息错误 [商家ID: %d]: %v", merchantID, err)
			}
			break
		}

		// 处理心跳消息
		if messageType == websocket.PingMessage {
			if err := conn.WriteMessage(websocket.PongMessage, nil); err != nil {
				log.Printf("发送心跳响应失败 [商家ID: %d]: %v", merchantID, err)
				break
			}
			continue
		}

		// 解析消息以检查类型
		var msgData struct {
			Type string `json:"type"`
		}
		if err := json.Unmarshal(message, &msgData); err != nil {
			log.Printf("解析消息失败 [商家ID: %d]: %v", merchantID, err)
			continue
		}

		// 只有非心跳消息才打印
		if msgData.Type != "heartbeat" {
			log.Printf("收到消息 [商家ID: %d]: %s", merchantID, string(message))
		}
	}
}

// SaveConnection 保存连接
func (h *WebSocketHandler) SaveConnection(merchantID int64, conn *websocket.Conn) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// 关闭旧连接
	if oldConn, exists := h.connections[merchantID]; exists {
		oldConn.Close()
	}

	h.connections[merchantID] = conn
}

// RemoveConnection 移除连接
func (h *WebSocketHandler) RemoveConnection(merchantID int64) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	delete(h.connections, merchantID)
}

// SendToMerchant 发送消息到指定商家
func (h *WebSocketHandler) SendToMerchant(merchantID uint64, message interface{}) error {
	h.mutex.RLock()
	conn, exists := h.connections[int64(merchantID)]
	h.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("no connection for merchant %d", merchantID)
	}

	return conn.WriteJSON(message)
}
