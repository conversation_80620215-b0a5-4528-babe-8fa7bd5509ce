import os
import logging
import logging.handlers
from datetime import datetime

def setup_logging(log_dir="logs", log_level=logging.INFO, console=True):
    """设置日志系统
    
    Args:
        log_dir (str): 日志文件目录
        log_level: 日志级别
        console (bool): 是否输出到控制台
    """
    # 创建日志目录
    os.makedirs(log_dir, exist_ok=True)
    
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 添加文件处理器
    log_file = os.path.join(
        log_dir, 
        f"printease_agent_{datetime.now().strftime('%Y%m%d')}.log"
    )
    file_handler = logging.handlers.RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # 添加控制台处理器
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger("websockets").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    return root_logger

class LogCapture:
    """日志捕获器，用于将日志记录到UI界面"""
    
    def __init__(self, max_lines=1000):
        """初始化日志捕获器
        
        Args:
            max_lines (int): 最大保存行数
        """
        self.logs = []
        self.max_lines = max_lines
        self.handler = None
        
    def setup(self):
        """设置日志捕获"""
        class UILogHandler(logging.Handler):
            def __init__(self, callback):
                super().__init__()
                self.callback = callback
                
            def emit(self, record):
                log_entry = self.format(record)
                self.callback(log_entry, record)
        
        # 创建处理器
        self.handler = UILogHandler(self._log_callback)
        self.handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        
        # 添加到根日志记录器
        logging.getLogger().addHandler(self.handler)
        
    def _log_callback(self, log_entry, record):
        """日志回调函数
        
        Args:
            log_entry (str): 格式化后的日志条目
            record: 日志记录对象
        """
        # 添加新日志
        self.logs.append({
            'time': datetime.fromtimestamp(record.created),
            'level': record.levelname,
            'message': record.getMessage(),
            'formatted': log_entry
        })
        
        # 限制日志数量
        if len(self.logs) > self.max_lines:
            self.logs = self.logs[-self.max_lines:]
    
    def get_logs(self, level=None, count=None):
        """获取日志
        
        Args:
            level (str, optional): 过滤日志级别
            count (int, optional): 返回的日志数量
            
        Returns:
            list: 日志列表
        """
        filtered_logs = self.logs
        
        # 按级别过滤
        if level:
            filtered_logs = [log for log in filtered_logs if log['level'] == level]
        
        # 限制数量
        if count:
            filtered_logs = filtered_logs[-count:]
            
        return filtered_logs
    
    def clear(self):
        """清除所有日志"""
        self.logs = [] 