<template>
	<view class="container">
		<!-- 顶部选择打印机 -->
		<view class="printer-select">
			<text class="title">选择打印机:</text>
			<text class="printer-name">黑白・黑白打印机</text>
			<text class="batch-settings">批量设置</text>
		</view>
		
		<!-- 文件列表 -->
		<view class="file-list">
			<view 
				class="file-item" 
				v-for="(file, index) in files" 
				:key="index"
			>
				<view class="file-main">
					<view class="file-header">
						<view class="file-index">{{String(index + 1).padStart(2, '0')}}</view>
						<view class="file-icon">
							<image 
								:src="getFileIcon(file.type)" 
								mode="aspectFit"
							></image>
						</view>
						<view class="file-content">
							<view class="file-name">{{file.name}}</view>
							<view class="file-settings">A4/黑白/单面/{{file.pages}}页/{{file.copies}}份</view>
						</view>
					</view>
					<view class="file-actions">
						<view class="action-btn" @tap="previewFile(file)">
							<image src="/static/icons/preview.png" mode="aspectFit"></image>
							<text>预览</text>
						</view>
						<view class="action-btn" @tap="showSettings(index)">
							<image src="/static/icons/settings.png" mode="aspectFit"></image>
							<text>设置</text>
						</view>
						<view class="action-btn" @tap="deleteFile(index)">
							<image src="/static/icons/delete.png" mode="aspectFit"></image>
							<text>删除</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部结算栏 -->
		<view class="bottom-bar">
			<view class="price-info">
				<view class="left">
					<text>合计:</text>
					<text class="total-price">￥{{totalPrice}}</text>
					<text class="original-price">￥{{originalPrice}}</text>
				</view>
				<button class="submit-btn" @tap="submitOrder" :disabled="files.length === 0">
					确认打印
				</button>
			</view>
		</view>
		
		<!-- 遮罩层 -->
		<view class="mask" :class="{ show: showSettingsPopup }" @tap="hideSettings"></view>
		
		<!-- 打印设置弹窗 -->
		<print-popup v-model="showSettingsPopup" type="bottom">
			<view class="settings-popup" :class="{ show: showSettingsPopup }" v-if="currentFile">
				<view class="popup-header">
					<view class="file-info">
						<image src="/static/icons/file.png" mode="aspectFit"></image>
						<text>{{currentFile.name}}</text>
					</view>
					<view class="close-btn" @tap="hideSettings">
						<image src="/static/icons/close.png" mode="aspectFit"></image>
					</view>
				</view>
				<view class="settings-content">
					<!-- 纸型选择 -->
					<view class="setting-item">
						<text class="setting-label">纸型</text>
						<view class="setting-value">
							<view class="setting-options">
								<text 
									:class="['option-btn', { active: currentFile.paperSize === 'A4' }]"
									@tap="setPaperSize('A4')"
								>A4</text>
								<text 
									:class="['option-btn', { active: currentFile.paperSize === 'A3' }]"
									@tap="setPaperSize('A3')"
								>A3</text>
							</view>
						</view>
					</view>
					
					<!-- 颜色选择 -->
					<view class="setting-item">
						<text class="setting-label">颜色</text>
						<view class="setting-value">
							<view class="setting-options">
								<text 
									:class="['option-btn', { active: currentFile.colorMode === 'black' }]"
									@tap="setColorMode('black')"
								>黑白</text>
							</view>
						</view>
					</view>
					
					<!-- 单双面选择 -->
					<view class="setting-item">
						<text class="setting-label">单双</text>
						<view class="setting-value">
							<view class="setting-options">
								<text 
									:class="['option-btn', { active: currentFile.sideMode === 'single' }]"
									@tap="setSideMode('single')"
								>单面</text>
								<text 
									:class="['option-btn', { active: currentFile.sideMode === 'double' }]"
									@tap="setSideMode('double')"
								>双面</text>
							</view>
						</view>
					</view>
					
					<!-- 份数设置 -->
					<view class="setting-item">
						<text class="setting-label">份数</text>
						<view class="setting-value">
							<view class="counter">
								<text 
									class="minus" 
									:class="{ disabled: currentFile.copies <= 1 }"
									@tap="decreaseCopies"
								>-</text>
								<input 
									type="number" 
									v-model.number="currentFile.copies"
									@input="validateCopies" 
								/>
								<text 
									class="plus"
									:class="{ disabled: currentFile.copies >= 99 }"
									@tap="increaseCopies"
								>+</text>
							</view>
						</view>
					</view>
					
					<!-- 多页合一 -->
					<view class="setting-item">
						<text class="setting-label">多页合一（缩印）</text>
						<view class="setting-value">
							<picker 
								:value="currentFile.pagesPerSheet ? currentFile.pagesPerSheet - 1 : 0" 
								:range="['每版打印1页', '每版打印2页', '每版打印4页', '每版打印6页', '每版打印8页','每版打印9页','每版打印16页']" 
								@change="setPagesPerSheet"
							>
								<view class="picker-value">
									{{ currentFile.pagesPerSheet ? '每版打印' + currentFile.pagesPerSheet + '页' : '每版打印1页' }} 
									<text class="arrow">></text>
								</view>
							</picker>
						</view>
					</view>
					
					<!-- 打印页面范围选择器 -->
					<view class="setting-item">
						<text class="setting-label">文档打印范围</text>
						<view class="setting-value">
							<view class="page-range">
								<view class="page-input-group">
									<text>第</text>
									<input 
										type="number" 
										v-model="currentFile.startPage" 
										@blur="validatePageRange(true)" 
										@input="validatePageRange(false)" 
									/>
									<text>-</text>
								</view>
								<view class="page-input-group">
									<input 
										type="number" 
										v-model="currentFile.endPage" 
										@blur="validatePageRange(true)" 
										@input="validatePageRange(false)" 
									/>
									<text>页</text>
									<text class="page-total" v-if="currentFile.pages">(共{{currentFile.pages}}页)</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="popup-footer">
					<button class="preview-btn" @tap="previewCurrentFile">预览</button>
					<button class="confirm-btn" @tap="confirmSettings">确认</button>
				</view>
			</view>
		</print-popup>
	</view>
</template>

<script>
import config from '@/config/config.js'
import {updateCartItemStatus, batchUpdateCartStatus, downloadFile, updateCartItemSettings, createOrder } from '@/api/index'
import { getFileIcon } from '@/utils/file'

export default {
	components: {
		'print-popup': () => import('@/components/print-popup.vue')
	},
	data() {
		return {
			files: [], // 购物车文件列表
			currentFile: null, // 当前编辑的文件
			currentIndex: -1, // 当前编辑的文件索引
			showSettingsPopup: false, // 改用不同的变量名，避免与方法名冲突
		}
	},
	computed: {
		totalPrice() {
			return '0.0'
		},
		originalPrice() {
			// Implement the logic to calculate the original price
			return '0.0'; // Placeholder return, actual implementation needed
		},
		getFileIcon() {
			return getFileIcon;
		}
	},
	methods: {
		// 显示设置弹窗
		showSettings(index) {
			this.currentIndex = index;
			const file = this.files[index];
			// 深拷贝文件对象
			this.currentFile = JSON.parse(JSON.stringify(file));
			
			// 确保使用正确的页数
			// 从file对象中获取页数，而不是调用getFilePages方法
			if (file.file && file.file.page_count) {
				this.currentFile.pages = file.file.page_count;
			} else {
				this.currentFile.pages = file.pages || 1;
			}
			
			console.log('打开设置面板，当前文件页数:', this.currentFile.pages);

			// 从config中提取设置数据（如果存在）
			if (file.config && typeof file.config === 'object') {
				// 应用config中的设置
				this.currentFile.paperSize = file.config.paperSize || file.paperSize || 'A4';
				this.currentFile.colorMode = file.config.colorMode || file.colorMode || 'black';
				this.currentFile.sideMode = file.config.sideMode || file.sideMode || 'single';
				this.currentFile.copies = parseInt(file.config.copies) || parseInt(file.copies) || 1;
				this.currentFile.pagesPerSheet = parseInt(file.config.pagesPerSheet) || parseInt(file.pagesPerSheet) || 1;
				this.currentFile.startPage = parseInt(file.config.startPage) || parseInt(file.startPage) || 1;
				this.currentFile.endPage = parseInt(file.config.endPage) || parseInt(file.endPage) || this.currentFile.pages || 1;
			} else {
				// 使用文件本身的属性
				this.currentFile.paperSize = file.paperSize || 'A4';
				this.currentFile.colorMode = file.colorMode || 'black';
				this.currentFile.sideMode = file.sideMode || 'single';
				this.currentFile.copies = parseInt(file.copies) || 1;
				this.currentFile.pagesPerSheet = parseInt(file.pagesPerSheet) || 1;
				this.currentFile.startPage = parseInt(file.startPage) || 1;
				this.currentFile.endPage = parseInt(file.endPage) || this.currentFile.pages || 1;
			}
			
			// 确保页码范围有效
			if (this.currentFile.startPage < 1) this.currentFile.startPage = 1;
			if (this.currentFile.endPage < this.currentFile.startPage) this.currentFile.endPage = this.currentFile.startPage;
			if (this.currentFile.endPage > this.currentFile.pages) this.currentFile.endPage = this.currentFile.pages;

			console.log('打开设置面板，当前文件配置:', {
				文件: file.name,
				配置详情: {
					纸型: this.currentFile.paperSize,
					颜色: this.currentFile.colorMode,
					单双面: this.currentFile.sideMode,
					份数: this.currentFile.copies,
					多页合一: this.currentFile.pagesPerSheet,
					起始页: this.currentFile.startPage,
					结束页: this.currentFile.endPage,
					总页数: this.currentFile.pages
				}
			});
			
			this.showSettingsPopup = true;
		},

		// 隐藏设置弹窗
		hideSettings() {
			this.showSettingsPopup = false; // 使用新的变量名
		},

		// 设置纸型
		setPaperSize(size) {
			if (this.currentFile) {
				this.currentFile.paperSize = size;
			}
		},

		// 设置颜色模式
		setColorMode(mode) {
			if (this.currentFile) {
				this.currentFile.colorMode = mode;
			}
		},

		// 设置单双面
		setSideMode(mode) {
			if (this.currentFile) {
				this.currentFile.sideMode = mode;
			}
		},

		// 增加份数
		increaseCopies() {
			if (this.currentFile && this.currentFile.copies < 99) {
				this.currentFile.copies++;
			}
		},

		// 减少份数
		decreaseCopies() {
			if (this.currentFile && this.currentFile.copies > 1) {
				this.currentFile.copies--;
			}
		},

		// 验证份数
		validateCopies() {
			if (!this.currentFile) return;
			
			const copies = this.currentFile.copies;
			if (isNaN(copies) || copies < 1) {
				this.currentFile.copies = 1;
			} else if (copies > 99) {
				this.currentFile.copies = 99;
			}
		},

		// 预览当前文件
		previewCurrentFile() {
			if (this.currentFile) {
				this.previewFile(this.currentFile);
			}
		},

		// 确认设置
		async confirmSettings() {
			if (this.currentIndex >= 0 && this.currentFile) {
				try {
					uni.showLoading({
						title: '保存中...'
					});
					
					// 创建完整的配置对象
					const configObj = {
						paperSize: this.currentFile.paperSize,
						colorMode: this.currentFile.colorMode,
						sideMode: this.currentFile.sideMode,
						copies: parseInt(this.currentFile.copies),
						pagesPerSheet: parseInt(this.currentFile.pagesPerSheet),
						startPage: parseInt(this.currentFile.startPage),
						endPage: parseInt(this.currentFile.endPage)
					};
					
					// 准备要发送到后端的设置数据
					const settings = {
						//paper_size: this.currentFile.paperSize,
						//color_mode: this.currentFile.colorMode,
						//side_mode: this.currentFile.sideMode,
						//copies: this.currentFile.copies,
						//pages_per_sheet: this.currentFile.pagesPerSheet || 1,
						//start_page: this.currentFile.startPage,
						//end_page: this.currentFile.endPage,
						config: JSON.stringify(configObj)
					};
					console.log('保存设置:', settings);
					
					// 调用API更新设置
					await updateCartItemSettings(this.currentFile.id, settings);
					
					// 更新前端文件设置
					this.files[this.currentIndex] = {
						...this.currentFile,
						config: configObj // 确保前端也保存了配置对象
					};
					
					uni.showToast({
						title: '设置已保存',
						icon: 'success'
					});
					
					this.hideSettings();
				} catch (error) {
					console.error('保存设置失败:', error);
					uni.showToast({
						title: '保存设置失败',
						icon: 'none'
					});
				} finally {
					uni.hideLoading();
				}
			}
		},

		// 初始化文件数据
		initFileData(file) {
			return {
				...file,
				pages: this.getFilePages(file),
				paperSize: 'A4',  // 默认设置为A4
				colorMode: 'black',
				sideMode: 'single',
				copies: 1,
				pagesPerSheet: 1,
				startPage: 1,
				endPage: this.getFilePages(file)
			};
		},

		// 删除文件
		deleteFile(index) {
			const cartItem = this.files[index]
			uni.showModal({
				title: '提示',
				content: '确定要删除该文件吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							// 调用更新状态接口，改为已删除状态 (CartStatusCancelled = 3)
							await updateCartItemStatus(cartItem.id, 3)
							// 重新加载购物车列表
							await this.loadCartItems()
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
						} catch (error) {
							console.error('删除失败:', error)
							uni.showToast({
								title: '删除失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},

		// 预览文件
		async previewFile(file) {
			uni.showLoading({
				title: '加载中...'
			})
			
			try {
				// 直接从后台下载文件
				const filePath = await downloadFile(file.file.id)
				
				// 直接打开文件预览
				await this.openFile(file.type, filePath)
			} catch (error) {
				console.error('预览文件失败:', error)
				uni.showToast({
					title: '预览失败',
					icon: 'none'
				})
			} finally {
				uni.hideLoading()
			}
		},

		// 打开文件的统一方法
		async openFile(fileType, filePath) {
			if (config.upload.allowedImageTypes.includes(fileType)) {
				// 图片预览
				uni.previewImage({
					urls: [filePath],
					current: 0
				})
			} else if (config.upload.allowedFileTypes.includes(fileType)) {
				// 文档预览 - 修改这部分代码
				// 将文件扩展名映射到小程序支持的文件类型
				let docType = fileType.toLowerCase();
				
				// 文件类型映射
				const typeMap = {
					'doc': 'doc',
					'docx': 'doc',
					'xls': 'xls',
					'xlsx': 'xls',
					'ppt': 'ppt',
					'pptx': 'ppt',
					'pdf': 'pdf',
					'txt': 'doc', // txt文件使用doc类型打开
					'rtf': 'doc'  // rtf文件使用doc类型打开
				};
				
				// 使用映射后的类型，如果没有映射则默认使用pdf
				const mappedType = typeMap[docType] || 'pdf';
				
				console.log('打开文档，原始类型:', fileType, '映射类型:', mappedType);
				
				uni.openDocument({
					filePath: filePath,
					fileType: mappedType,  // 使用映射后的文件类型
					success: () => {
						console.log('打开文档成功')
					},
					fail: (err) => {
						console.error('打开文档失败:', err)
						uni.showToast({
							title: '无法预览该文件',
							icon: 'none'
						})
					}
				})
			} else {
				uni.showToast({
					title: '不支持的文件类型',
					icon: 'none'
				})
			}
		},

		// 获取文件页数，直接从文件信息中读取
		getFilePages(file) {
			// 检查是否传入了file参数，如果没有则使用this.currentFile
			const fileObj = file || this.currentFile;
			
			// 如果fileObj仍然为空，返回默认值1
			if (!fileObj) {
				console.error('文件对象为空');
				return 1;
			}
			
			// 如果有pageCount字段就直接使用
			if (fileObj.page_count && fileObj.page_count > 0) {
				console.log(`文件${fileObj.filename || '未知文件'}的页数为:`, fileObj.page_count);
				return fileObj.page_count;
			}
			
			// 根据文件类型设置默认页数
			let defaultPages = 1;
			
			// 对于图片文件，默认为1页
			const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'tif', 'tiff'];
			if (fileObj.file_type && imageTypes.includes(fileObj.file_type.toLowerCase())) {
				defaultPages = 1;
			}
			
			console.log(`无法获取文件${fileObj.filename || '未知文件'}的页数信息，使用默认值:`, defaultPages);
			return defaultPages;
		},

		// 确认打印
		async submitOrder() {
			if (this.files.length === 0) {
				uni.showToast({
					title: '请先添加文件',
					icon: 'none'
				});
				return;
			}
			
			// 先检查Vuex中是否已有选中的打印店
			const selectedStore = this.$store.getters.getSelectedStore;
			
			if (selectedStore) {
				// 如果已有选中的打印店，直接使用
				console.log('使用已选中的打印店:', selectedStore);
				await this.processOrder(selectedStore);
			} else {
				// 如果没有选中的打印店，跳转到选择页面
				uni.navigateTo({
					url: '/pages/stores/stores?action=select',
					events: {
						// 用户选择了打印店
						selectStore: async (store) => {
							console.log('用户选择的打印店:', store);
							// 将选中的打印店保存到Vuex
							this.$store.commit('setSelectedStore', store);
							await this.processOrder(store);
						}
					}
				});
			}
		},

		// 处理订单的方法，抽取公共逻辑
		async processOrder(store) {
			try {
				uni.showLoading({
					title: '提交订单中...'
				});
				
				// 获取所有选中的CartID
				const cartIds = this.files.map(file => file.id);
				
				// 创建订单请求
				const orderData = {
					user_id: this.getUserId(), // 获取用户ID
					store_id: store.id,
					cart_ids: cartIds,
					remark: '' // 订单备注，可以在界面上添加输入框让用户填写
				};
				
				console.log('创建订单参数:', orderData);
				
				// 调用创建订单API
				const res = await createOrder(orderData);
				
				if (res && res.code === 0) {
					console.log('订单创建成功:', res.data);
					
					uni.showToast({
						title: '订单创建成功',
						icon: 'success'
					});
					
					// 跳转到订单详情页
					setTimeout(() => {
						uni.navigateTo({
							url: `/pages/order_detail/order_detail?id=${res.data.id}`
						});
					}, 1500);
				} else {
					console.error('创建订单失败:', res);
					uni.showToast({
						title: res.message || '创建订单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('提交订单出错:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},
		
		// 获取用户ID的方法，根据实际登录系统修改
		getUserId() {
			// 这里应该从全局状态或存储中获取用户ID
			console.log('获取用户ID');
			return 1; // 测试用，实际应从用户系统获取
		},

		// 加载购物车列表
		async loadCartItems() {
			try {
				console.log('使用的基础URL:', config.api.baseUrl);
				const response = await uni.request({
					url: config.api.baseUrl + '/api/cart-items',
					method: 'GET',
					data: {
						user_id: this.userId || 1
					}
				});
				
				if (response.statusCode === 200) {
					// 处理响应数据，确保文件对象具有所需的属性
					this.files = (response.data.data || []).map(item => {
						// 解析配置
						let configObj = {};
						try {
							if (item.config && typeof item.config === 'string') {
								configObj = JSON.parse(item.config);
							} else if (item.config && typeof item.config === 'object') {
								configObj = item.config;
							}
						} catch (e) {
							console.error('解析配置失败:', e, item.config);
						}
						
						// 创建文件对象，优先使用config中的设置
						return {
							id: item.id,
							file: item.file, // 保持完整的file对象
							name: item.file?.filename || '未命名文件',
							type: item.file?.file_type || 'unknown',
							size: item.file?.file_size || 0,
							path: item.file?.file_path || '',
							pages: item.file?.page_count || configObj.pages || 1,
							paperSize: configObj.paperSize || item.paper_size || 'A4',
							colorMode: configObj.colorMode || item.color_mode || 'black',
							sideMode: configObj.sideMode || item.side_mode || 'single',
							copies: configObj.copies || item.copies || 1,
							pagesPerSheet: configObj.pagesPerSheet || item.pages_per_sheet || 1,
							startPage: configObj.startPage || item.start_page || 1,
							endPage: configObj.endPage || item.end_page || item.file?.page_count || 1,
							config: configObj
						};
					});	
					
					console.log('购物车列表加载成功:', this.files);
				}
			} catch (error) {
				console.error('获取购物车列表失败:', error);
			}
		},

		// 计算总价
		calculateTotal() {
			// 初始化总价和原价
			let totalPrice = 0;
			let originalPrice = 0;
			
			// 遍历所有文件计算价格
			this.files.forEach(file => {
				// 基础价格计算 (这里使用简单的计算逻辑，可以根据实际需求调整)
				const basePrice = 0.5; // 每页基础价格
				const pages = parseInt(file.pages || 1);
				const copies = parseInt(file.copies || 1);
				
				// 计算单个文件价格
				let filePrice = basePrice * pages * copies;
				
				// 双面打印可能有折扣
				if (file.sideMode === 'double') {
					filePrice *= 0.8; // 双面打印打8折
				}
				
				// 彩色打印可能更贵
				if (file.colorMode === 'color') {
					filePrice *= 2; // 彩色打印价格翻倍
				}
				
				// 累加到总价
				totalPrice += filePrice;
				
				// 原价（不打折的价格）
				originalPrice += basePrice * pages * copies;
			});
			
			// 更新数据
			this.totalPrice = totalPrice.toFixed(2);
			this.originalPrice = originalPrice.toFixed(2);
			
			console.log('计算总价:', this.totalPrice, '原价:', this.originalPrice);
		},

		// 设置多页合一
		setPagesPerSheet(event) {
			if (this.currentFile) {
				const selectedIndex = event.detail.value;
				// 根据索引获取对应的页数值
				const pagesPerSheetOptions = [1, 2, 4, 6, 8, 9, 16];
				this.currentFile.pagesPerSheet = pagesPerSheetOptions[selectedIndex];
			}
		},

		// 验证页码范围
		validatePageRange(isBlur = false) {
			if (!this.currentFile) return;
			
			// 只在失焦时进行验证和修正，输入过程中不干预
			if (!isBlur) {
				// 输入过程中只记录日志，不修改值
				console.log('输入中:', {
					起始页: this.currentFile.startPage,
					结束页: this.currentFile.endPage
				});
				return; // 直接返回，不进行任何验证或修改
			}
			
			// 以下代码只在失焦时执行
			
			// 处理有效的数字输入，避免处理空字符串
			let startPage = this.currentFile.startPage === '' ? '' : parseInt(this.currentFile.startPage);
			let endPage = this.currentFile.endPage === '' ? '' : parseInt(this.currentFile.endPage);
			let totalPages = parseInt(this.currentFile.pages || 0);
			
			// 确保总页数有效
			if (isNaN(totalPages) || totalPages < 1) {
				totalPages = 1;
			}
			
			// 错误提示信息
			let errorMessage = '';
			
			// 验证起始页
			if (startPage === '' || isNaN(startPage)) {
				errorMessage = '起始页不能为空';
				startPage = 1;
			} else if (startPage < 1) {
				errorMessage = '起始页不能小于1';
				startPage = 1;
			} else if (startPage > totalPages) {
				errorMessage = `起始页不能大于总页数(${totalPages})`;
				startPage = 1;
			}
			
			// 验证结束页
			if (endPage === '' || isNaN(endPage)) {
				errorMessage = '结束页不能为空';
				endPage = totalPages;
			} else if (endPage < 1) {
				errorMessage = '结束页不能小于1';
				endPage = totalPages;
			} else if (endPage > totalPages) {
				errorMessage = `结束页不能大于总页数(${totalPages})`;
				endPage = totalPages;
			}
			
			// 确保结束页不小于起始页
			if (endPage < startPage) {
				errorMessage = '结束页不能小于起始页';
				startPage = 1;
				endPage = totalPages;
			}
			
			// 显示错误提示并修正值
			if (errorMessage) {
				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 2000
				});
				
				// 小程序特殊处理：先清空值，再设置新值
				const tempFile = {...this.currentFile};
				
				// 1. 先清空值
				this.currentFile.startPage = '';
				this.currentFile.endPage = '';
				
				// 2. 使用setTimeout延迟设置新值
				setTimeout(() => {
					// 3. 设置新值
					this.currentFile.startPage = String(startPage);
					this.currentFile.endPage = String(endPage);
					
					// 4. 强制更新视图
					this.$forceUpdate();
					
					console.log('已重置输入框值:', {
						新起始页: this.currentFile.startPage,
						新结束页: this.currentFile.endPage
					});
				}, 50);
			}
			
			console.log('验证页码范围:', {
				起始页: this.currentFile.startPage,
				结束页: this.currentFile.endPage,
				总页数: totalPages,
				是否失焦: isBlur,
				错误信息: errorMessage
			});
		},

		// 保存打印设置
		savePrintSettings() {
			// 验证页面范围
			const settings = this.currentEditItem.settings;
			const totalPages = this.currentEditItem.totalPages;
			
			// 确保起始页不小于1
			settings.startPage = Math.max(1, parseInt(settings.startPage) || 1);
			
			// 确保结束页不大于总页数
			settings.endPage = Math.min(totalPages, parseInt(settings.endPage) || totalPages);
			
			// 确保起始页不大于结束页
			if (settings.startPage > settings.endPage) {
				settings.startPage = settings.endPage;
			}
			
			// 将设置序列化为JSON字符串
			const configStr = JSON.stringify(settings);
			
			// 调用API保存设置
			this.updateCartItemSettings(this.currentEditItem.id, {
				config: configStr
			}).then(() => {
				// 关闭设置弹窗
				this.showPrintSettings = false;
				// 刷新购物车数据
				this.loadCartItems();
				// 显示成功提示
				uni.showToast({
					title: '设置已保存',
					icon: 'success'
				});
			}).catch(err => {
				console.error('保存设置失败:', err);
				uni.showToast({
					title: '保存设置失败',
					icon: 'none'
				});
			});
		}
	},
	onLoad() {
		this.loadCartItems()
	}
}
</script>

<style lang="scss">
.container {
	min-height: 100vh;
	background: #f5f5f5;
	font-family: sans-serif;
	font-size: 28rpx;
	position: relative;
	height: 100vh;
	overflow: hidden;
}

// 状态栏样式
.status-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 30rpx;
	background: #fff;
	color: #000;

	.left {
		display: flex;
		align-items: center;

		.signal {
			margin-left: 10rpx;
			font-size: 24rpx;
			padding: 0 10rpx;
			background: #ff0000;
			color: #fff;
			border-radius: 8rpx;
		}

		.icon {
			margin-left: 10rpx;
			padding: 5rpx;
			border-radius: 8rpx;

			&.lock {
				background: #fbbf24;
			}

			&.battery {
				background: #f97316;
			}
		}
	}

	.right {
		display: flex;
		align-items: center;

		.hd {
			margin-right: 10rpx;
			font-size: 24rpx;
		}

		.signal-bars {
			display: flex;
			height: 30rpx;
			align-items: flex-end;
			margin-right: 20rpx;

			.bar {
				width: 10rpx;
				margin: 0 5rpx;
				background: #000;

				&.active {
					background: #000;
				}

				&.inactive {
					background: #9ca3af;
				}
			}
		}

		.battery-level {
			display: flex;
			align-items: center;
			border: 2rpx solid #d1d5db;
			border-radius: 12rpx;
			padding: 0 10rpx;
		}
	}
}

// 头部导航栏
.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	background: #fff;
	border-bottom: 2rpx solid #eee;

	.title {
		font-size: 36rpx;
		font-weight: 500;
	}

	.actions {
		display: flex;

		.action-btn {
			margin-left: 40rpx;
		}
	}
}

// 打印机选择栏
.printer-select {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 30rpx;
	background: #6b7280;
	color: #fff;

	.title {
		color: #fca5a5;
	}

	.right {
		display: flex;
		align-items: center;

		.batch-settings {
			margin-left: 40rpx;
		}
	}
}

// 文件列表
.file-list {
	padding: 30rpx;
	background: #f5f5f5;
	flex: 1;
	overflow-y: auto;
	height: calc(100vh - 300rpx);

	.file-item {
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 30rpx;

		.file-main {
			.file-header {
				display: flex;
				align-items: center;
				margin-bottom: 30rpx;

				.file-index {
					width: 60rpx;
					color: #9ca3af;
					font-size: 28rpx;
				}

				.file-icon {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 20rpx;

					image {
						width: 80rpx;
						height: 80rpx;
						color: #fff;
					}
				}

				.file-content {
					flex: 1;
					overflow: hidden;

					.file-name {
						font-size: 28rpx;
						font-weight: normal;
						color: #333;
						margin-bottom: 10rpx;
						word-break: break-all;
						line-height: 1.4;
						padding-right: 20rpx;
					}

					.file-settings {
						font-size: 24rpx;
						color: #6b7280;
					}
				}
			}

			.file-actions {
				display: flex;
				justify-content: flex-end;
				padding-left: 110rpx;
				gap: 20rpx;
				flex-wrap: nowrap;

				.action-btn {
					display: flex;
					align-items: center;
					padding: 12rpx 24rpx;
					min-width: fit-content;
					border: 2rpx solid #e5e7eb;
					border-radius: 100rpx;
					color: #6b7280;
					font-size: 28rpx;
					white-space: nowrap;

					image {
						width: 32rpx;
						height: 32rpx;
						margin-right: 10rpx;
						flex-shrink: 0;
					}
				}
			}
		}
	}
}

// 添加遮罩层
.mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 998;
	display: none;

	&.show {
		display: block;
	}
}

// 修改设置弹窗样式
.settings-popup {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background: #fff;
	border-radius: 20rpx 20rpx 0 0;
	height: 1000rpx;
	z-index: 999;
	transform: translateY(100%);
	transition: transform 0.3s ease-out;

	&.show {
		transform: translateY(0);
	}

	.popup-header {
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.file-info {
			display: flex;
			align-items: center;
			flex: 1;
			overflow: hidden;
			
			image {
				width: 48rpx;
				height: 48rpx;
				margin-right: 20rpx;
			}
			
			text {
				font-size: 28rpx;
				color: #333;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				max-width: 500rpx;
			}
		}
		
		.close-btn {
			padding: 10rpx;
			margin-left: 20rpx;
			
			image {
				width: 32rpx;
				height: 32rpx;
			}
		}
	}
	
	.settings-content {
		flex: 1;
		padding: 0 20rpx;
		padding-bottom: 120rpx;	
		
		.setting-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 40rpx 10rpx;
			border-bottom: 1rpx solid #eee;
			
			&:last-child {
				border-bottom: none;
			}
			
			.setting-label {
				font-size: 28rpx;
				color: #333;
				min-width: 160rpx;
				padding-top: 0;
			}
			
			.setting-value {
				flex: 1;
				display: flex;
				justify-content: flex-end;
				align-items: center;
				
				.setting-text {
					font-size: 28rpx;
					color: #666;
				}
				
				.setting-options {
					display: flex;
					gap: 20rpx;
					
					.option-btn {
						padding: 15rpx 40rpx;
						font-size: 28rpx;
						color: #333;
						background: #f5f5f5;
						border-radius: 8rpx;
						
						&.active {
							background: #4cd964;
							color: #fff;
						}
					}
				}
				
				.counter {
					display: flex;
					align-items: center;
					
					text {
						width: 60rpx;
						height: 60rpx;
						line-height: 60rpx;
						text-align: center;
						background: #f5f5f5;
						border-radius: 8rpx;
						
						&.disabled {
							color: #ccc;
						}
					}
					
					input {
						width: 100rpx;
						height: 60rpx;
						line-height: 60rpx;
						text-align: center;
						margin: 0 20rpx;
						font-size: 28rpx;
					}
				}
			}
		}
	}
	
	.popup-footer {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 20rpx 30rpx;
		border-top: 1rpx solid #eee;
		background: #fff;
		display: flex;
		gap: 20rpx;
		
		button {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			border-radius: 40rpx;
			
			&.preview-btn {
				background: #fff;
				border: 1rpx solid #4cd964;
				color: #4cd964;
			}
			
			&.confirm-btn {
				background: #4cd964;
				color: #fff;
			}
		}
	}
}

// 底部栏
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #fff;

	.price-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;

		.left {
			display: flex;
			align-items: baseline;

			text:first-child {
				margin-right: 10rpx;
				font-size: 28rpx;
			}

			.total-price {
				color: #ef4444;
				font-size: 36rpx;
				font-weight: 500;
			}

			.original-price {
				color: #9ca3af;
				text-decoration: line-through;
				margin-left: 20rpx;
				font-size: 28rpx;
			}
		}

		.submit-btn {
			background: #22c55e;
			color: #fff;
			padding: 20rpx 60rpx;
			border-radius: 5px;
			font-size: 28rpx;
			margin-right: 10px;
			min-width: 200rpx;
			text-align: center;

			&:disabled {
				opacity: 0.5;
			}
		}
	}
}

// 文件信息显示部分
.file-info {
	display: flex;
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
	
	.file-size, .file-pages {
		margin-right: 20rpx;
	}
	
	.file-pages {
		color: #1aad19; // 使用主题色突出显示页数
	}
}

.file-meta {
	display: flex;
	font-size: 24rpx;
	color: #666;
	margin-top: 8rpx;
	
	.file-size, .file-pages {
		margin-right: 20rpx;
	}
	
	.file-pages {
		color: #1aad19; // 使用主题色突出显示页数
	}
}

.page-total {
	margin-top: 10rpx;
	font-size: 24rpx;
	color: #666;
}

// 添加页面范围选择器的样式
.page-range {
	display: flex;
	align-items: center;
	
	.page-input-group {
		display: flex;
		align-items: center;
		
		text {
			margin: 0 10rpx;
			color: #666;
		}
		
		input {
			width: 80rpx;
			height: 60rpx;
			background: #f5f5f5;
			border-radius: 8rpx;
			text-align: center;
			font-size: 28rpx;
		}
	}
	
	.page-total {
		margin-left: 10rpx;
		color: #999;
		font-size: 24rpx;
	}
}
</style>

