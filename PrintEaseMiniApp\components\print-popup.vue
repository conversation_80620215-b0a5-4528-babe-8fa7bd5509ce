<template>
  <view class="popup" v-if="show">
    <view class="popup-mask" @tap="close"></view>
    <view class="popup-content" :class="type">
      <slot></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PrintPopup',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'bottom'
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false)
    }
  }
}
</script>

<style lang="scss">
.popup {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  
  .popup-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.4);
  }
  
  .popup-content {
    position: absolute;
    background: #fff;
    transition: all 0.3s;
    
    &.bottom {
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 20rpx 20rpx 0 0;
      transform: translateY(0);
    }
  }
}
</style> 