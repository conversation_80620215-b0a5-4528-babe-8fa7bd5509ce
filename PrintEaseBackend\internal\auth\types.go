package auth

import (
	"github.com/golang-jwt/jwt/v4"
)

// TokenType 定义token类型
type TokenType string

const (
	TokenTypeBearer TokenType = "Bearer"
)

// ClientType 定义客户端类型
type ClientType string

const (
	ClientTypeMini   ClientType = "miniapp" // 小程序（预留）
	ClientTypeClient ClientType = "client"  // 客户端（预留）
	ClientTypeWeb    ClientType = "web"     // 网页版（预留）
	ClientTypeMp     ClientType = "mp"      // 公众号（预留）
	ClientTypeAgent  ClientType = "agent"   // 代理程序
)

// RoleType 定义角色类型
type RoleType string

const (
	RoleUser          RoleType = "user"  // 用户（预留）
	RoleMerchantOwner RoleType = "owner" // 店主
	RoleMerchantStaff RoleType = "staff" // 店员
	RoleAdmin         RoleType = "admin" // 管理员（预留）
)

// Claims JWT载荷结构
type Claims struct {
	UserID     int64      `json:"user_id"`
	Username   string     `json:"username"`
	StoreID    int64      `json:"store_id"`
	StoreName  string     `json:"store_name"`
	ClientType ClientType `json:"client_type"`
	RoleType   RoleType   `json:"role_type"`
	jwt.RegisteredClaims
}
