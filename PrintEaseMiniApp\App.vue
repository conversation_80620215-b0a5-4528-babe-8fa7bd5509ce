<script>
import store from './store'

export default {
	store,
	onLaunch: function() {
		console.log('App Launch');
		// 检查更新
		this.checkUpdate();
		// 获取系统信息
		this.getSystemInfo();
	},
	onShow: function() {
		console.log('App Show')
	},
	onHide: function() {
		console.log('App Hide')
	},
	methods: {
		// 检查更新
		checkUpdate() {
			// #ifdef MP-WEIXIN
			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				if (res.hasUpdate) {
					updateManager.onUpdateReady(function() {
						uni.showModal({
							title: '更新提示',
							content: '新版本已经准备好，是否重启应用？',
							success: function(res) {
								if (res.confirm) {
									updateManager.applyUpdate();
								}
							}
						});
					});
				}
			});
			// #endif
		},
		// 获取系统信息
		getSystemInfo() {
			try {
				// 获取窗口信息
				const windowInfo = uni.getWindowInfo();
				// 获取应用基础信息
				const appBaseInfo = uni.getAppBaseInfo();
				// 获取系统设置
				const systemSetting = uni.getSystemSetting();
				
				// 状态栏高度
				uni.setStorageSync('statusBarHeight', systemSetting.statusBarHeight);
				// 导航栏高度
				uni.setStorageSync('navBarHeight', appBaseInfo.platform === 'ios' ? 44 : 48);
			} catch (e) {
				console.error('获取系统信息失败:', e);
			}
		}
	}
}
</script>

<style lang="scss">
	/* 全局样式 */
	page {
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
			Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
			sans-serif;
		background-color: #f8f8f8;
	}

	/* 通用样式类 */
	.flex-row {
		display: flex;
		flex-direction: row;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.justify-center {
		justify-content: center;
	}

	.align-center {
		align-items: center;
	}

	.text-center {
		text-align: center;
	}

	/* 安全区适配 */
	.safe-area-inset-bottom {
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	/* 主题色 */
	:root {
		--primary-color: #1aad19;
		--primary-color-light: #2bbb2a;
		--primary-color-dark: #179b16;
	}
</style>
