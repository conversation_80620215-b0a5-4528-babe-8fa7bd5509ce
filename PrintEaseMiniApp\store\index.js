import { createStore } from 'vuex'

const store = createStore({
	state: {
		userInfo: uni.getStorageSync('userInfo') || null,
		token: uni.getStorageSync('token') || null,
		selectedStore: uni.getStorageSync('selectedStore') || null
	},
	mutations: {
		setUserInfo(state, userInfo) {
			state.userInfo = userInfo
			uni.setStorageSync('userInfo', userInfo)
		},
		setToken(state, token) {
			state.token = token
			uni.setStorageSync('token', token)
		},
		setSelectedStore(state, store) {
			state.selectedStore = store
			uni.setStorageSync('selectedStore', store)
		},
		logout(state) {
			state.userInfo = null
			state.token = null
			state.selectedStore = null
			uni.removeStorageSync('userInfo')
			uni.removeStorageSync('token')
			uni.removeStorageSync('selectedStore')
		}
	},
	getters: {
		getSelectedStore: state => state.selectedStore
	}
})

export default store 
