package models

import (
	"PrintEaseBackend/internal/database"
	"time"
)

// Order 订单模型
type Order struct {
	ID         uint64      `gorm:"primaryKey;column:id" json:"id"`
	UserID     uint64      `gorm:"not null;column:user_id" json:"user_id"`     // 用户ID
	StoreID    uint64      `gorm:"not null;column:store_id" json:"store_id"`   // 商店ID
	TotalFee   float64     `gorm:"not null;column:total_fee" json:"total_fee"` // 订单总金额
	Status     int8        `gorm:"default:0;column:status" json:"status"`      // 状态
	Remark     string      `gorm:"column:remark;type:text" json:"remark"`      // 订单备注
	CreatedAt  time.Time   `gorm:"column:created_at" json:"created_at"`
	UpdatedAt  time.Time   `gorm:"column:updated_at" json:"updated_at"`
	User       User        `gorm:"foreignKey:UserID" json:"-"`            // 关联用户
	Store      Store       `gorm:"foreignKey:StoreID" json:"store"`       // 关联商店
	OrderItems []OrderItem `gorm:"foreignKey:OrderID" json:"order_items"` // 关联订单项
}

// OrderItem 订单项模型
type OrderItem struct {
	ID         uint64    `gorm:"primaryKey;column:id" json:"id"`
	OrderID    uint64    `gorm:"not null;column:order_id" json:"order_id"`         // 订单ID
	CartItemID uint64    `gorm:"not null;column:cart_item_id" json:"cart_item_id"` // 购物车项ID
	Price      float64   `gorm:"not null;column:price" json:"price"`               // 价格
	Status     int8      `gorm:"default:0;column:status" json:"status"`            // 状态
	CreatedAt  time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at" json:"updated_at"`
	CartItem   CartItem  `gorm:"foreignKey:CartItemID" json:"cart_item"` // 关联购物车项
}

// 订单状态常量
const (
	OrderStatusPending   int8 = 0 // 待支付
	OrderStatusPaid      int8 = 1 // 已支付
	OrderStatusPrinting  int8 = 2 // 打印中
	OrderStatusCompleted int8 = 3 // 已完成
	OrderStatusCancelled int8 = 4 // 已取消
	OrderStatusRefunded  int8 = 5 // 已退款
)

// CreateOrder 创建订单
func CreateOrder(order *Order) error {
	return database.DB.Create(order).Error
}

// GetOrderByID 根据ID获取订单
func GetOrderByID(id uint64) (*Order, error) {
	var order Order
	err := database.DB.Preload("User").Preload("Store").Preload("OrderItems.CartItem.File").First(&order, id).Error
	return &order, err
}

// GetOrdersByUserID 获取用户的订单列表
func GetOrdersByUserID(userID uint64) ([]Order, error) {
	var orders []Order
	err := database.DB.Where("user_id = ?", userID).Preload("User").Preload("Store").Preload("OrderItems.CartItem.File").Find(&orders).Error
	return orders, err
}

// UpdateOrderStatus 更新订单状态
func UpdateOrderStatus(id uint64, status int8) error {
	return database.DB.Model(&Order{}).Where("id = ?", id).Update("status", status).Error
}

// TableName 指定表名
func (Order) TableName() string {
	return "orders"
}

// TableName 指定表名
func (OrderItem) TableName() string {
	return "order_items"
}
