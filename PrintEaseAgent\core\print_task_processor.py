import os
import json
import logging
import time
import threading
from queue import Queue, Empty

from .database import PrintTaskManager, PrinterManager
from .print_manager import PrintManager

logger = logging.getLogger(__name__)

class PrintTaskProcessor:
    """打印任务处理器，负责处理打印任务队列"""
    
    def __init__(self, uploads_dir="uploads"):
        """初始化打印任务处理器
        
        Args:
            uploads_dir: 上传文件保存目录
        """
        self.uploads_dir = uploads_dir
        self.task_manager = PrintTaskManager()
        self.printer_manager = PrinterManager()
        self.print_manager = PrintManager()
        
        # 任务队列
        self.task_queue = Queue()
        
        # 处理线程
        self.processing_thread = None
        self.is_running = False
        
        logger.info("打印任务处理器初始化完成")
    
    def start(self):
        """启动任务处理线程"""
        try:
            if self.processing_thread is not None and self.processing_thread.is_alive():
                logger.warning("打印任务处理线程已在运行")
                return
            
            self.is_running = True
            self.processing_thread = threading.Thread(target=self._process_tasks, daemon=True)
            self.processing_thread.start()
            logger.info("打印任务处理线程已启动")
        except Exception as e:
            logger.error(f"启动打印任务处理线程失败: {str(e)}")
              
    def stop(self):
        """停止任务处理线程"""
        self.is_running = False
        if self.processing_thread is not None:
            self.processing_thread.join(timeout=2.0)
            logger.info("打印任务处理线程已停止")
    
    def add_task(self, task_id: str):
        """添加任务到队列
        
        Args:
            task_id: 任务ID
        """
        self.task_queue.put(task_id)
        logger.info(f"任务 {task_id} 已添加到队列")
    
    def process_pending_tasks(self):
        """处理所有待处理的任务"""
        # 获取所有等待中的任务
        pending_tasks = self.task_manager.get_tasks_by_status("waiting")
        
        # 添加到队列
        for task in pending_tasks:
            self.add_task(task["task_id"])
        
        logger.info(f"已添加 {len(pending_tasks)} 个待处理任务到队列")
    
    def _process_tasks(self):
        """处理任务队列的线程函数"""
        logger.info("任务处理线程开始运行")
        logger.info(f"初始状态 - is_running: {self.is_running}, 队列大小: {self.task_queue.qsize()}")

        while self.is_running:
            try:
                # 从队列获取任务，最多等待1秒
                try:
                    task_id = self.task_queue.get(timeout=1.0)
                except Empty:
                    continue
                
                # 处理任务
                self._process_task(task_id)
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except Exception as e:
                logger.error(f"处理任务时发生错误: {str(e)}")
                time.sleep(1.0)  # 出错时暂停一下
        
        logger.info("任务处理线程已退出")
    
    def _process_task(self, task_id: str):
        """处理单个打印任务
        
        Args:
            task_id: 任务ID
        """
        logger.info(f"开始处理任务: {task_id}")
        
        try:
            # 获取任务信息
            task = self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return
            
            # 检查任务状态
            if task["status"] != "waiting":
                logger.warning(f"任务 {task_id} 状态为 {task['status']}，跳过处理")
                return
            
            # 更新任务状态为处理中
            self.task_manager.update_task_status(task_id, "processing")
            
            # 获取打印配置
            print_config = {}
            if task["print_config"]:
                try:
                    print_config = json.loads(task["print_config"])
                except:
                    logger.error(f"解析打印配置失败: {task['print_config']}")
            
            # 获取打印机
            printer_id = task.get("printer_id")
            printer_name = None
            
            if printer_id:
                # 如果任务已分配打印机，使用指定的打印机
                printer = self.printer_manager.get_printer_by_id(printer_id)
                if printer:
                    printer_name = printer["system_name"]
            
            # TODO：根据打印配置自动选择打印机
            if not printer_name:
                # 如果未指定打印机或找不到指定的打印机，使用默认打印机
                printer_name = self.print_manager.get_default_printer()
                # 获取默认打印机的ID并更新到数据库
                default_printer = self.printer_manager.get_printer_by_system_name(printer_name, 'windows')
                if default_printer:
                    printer_id = default_printer["id"]
                    # 使用self.assign_printer而不是self.task_manager.assign_printer
                    self.assign_printer(task_id, printer_id)
            
            # 构建文件路径
            # TODO：uploads文件路径，后续需要更新
            file_path = os.path.join(self.uploads_dir, task["filename"])
            if not os.path.exists(file_path):
                error_msg = f"文件不存在: {file_path}"
                logger.error(error_msg)
                self.task_manager.update_task_status(task_id, "failed", error_message=error_msg)
                return
            
            # 更新任务状态为打印中
            self.task_manager.update_task_status(task_id, "printing")
            
            # 打印文件
            success = self.print_manager.print_file(file_path, printer_name, print_config)
            
            if success:
                # 更新任务状态为已完成
                self.task_manager.update_task_status(task_id, "completed")
                logger.info(f"任务 {task_id} 打印成功")
            else:
                # 更新任务状态为失败
                error_msg = "打印失败"
                self.task_manager.update_task_status(task_id, "failed", error_message=error_msg)
                logger.error(f"任务 {task_id} 打印失败")
            
        except Exception as e:
            # 更新任务状态为失败
            error_msg = f"处理任务时出错: {str(e)}"
            self.task_manager.update_task_status(task_id, "failed", error_message=error_msg)
            logger.error(error_msg)
    
    def retry_failed_task(self, task_id: str) -> bool:
        """重试失败的任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功将任务重新加入队列
        """
        try:
            # 获取任务信息
            task = self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            # 检查任务状态
            if task["status"] not in ["failed", "cancelled"]:
                logger.warning(f"任务 {task_id} 状态为 {task['status']}，不需要重试")
                return False
            
            # 增加重试次数
            self.task_manager.increment_retry_count(task_id)
            
            # 更新任务状态为等待中
            self.task_manager.update_task_status(task_id, "waiting")
            
            # 添加到队列
            self.add_task(task_id)
            
            logger.info(f"任务 {task_id} 已重新加入队列")
            return True
            
        except Exception as e:
            logger.error(f"重试任务 {task_id} 失败: {str(e)}")
            return False
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功取消任务
        """
        try:
            # 获取任务信息
            task = self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            # 检查任务状态
            if task["status"] in ["completed", "cancelled"]:
                logger.warning(f"任务 {task_id} 状态为 {task['status']}，无法取消")
                return False
            
            # 更新任务状态为已取消
            self.task_manager.update_task_status(task_id, "cancelled")
            
            logger.info(f"任务 {task_id} 已取消")
            return True
            
        except Exception as e:
            logger.error(f"取消任务 {task_id} 失败: {str(e)}")
            return False
    
    def assign_printer(self, task_id: str, printer_id: str) -> bool:
        """为任务分配打印机
        
        Args:
            task_id: 任务ID
            printer_id: 打印机ID
        
        Returns:
            bool: 是否成功分配打印机
        """
        try:
            # 获取任务信息
            task = self.task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return False
            
            # 检查打印机是否存在
            printer = self.printer_manager.get_printer_by_id(printer_id)
            if not printer:
                logger.error(f"打印机 {printer_id} 不存在")
                return False
            
            # 分配打印机
            # TODO：是否需要在这里检查待打印文件的配置信息，和打印机是否符合，比如会不会将一个彩色文件分配给一个黑白打印机？
            success = self.task_manager.update_task_printer(task_id, printer_id)
            
            if success:
                logger.info(f"已为任务 {task_id} 分配打印机 {printer_id}")
            else:
                logger.error(f"为任务 {task_id} 分配打印机失败")
            
            return success
            
        except Exception as e:
            logger.error(f"为任务 {task_id} 分配打印机失败: {str(e)}")
            return False






