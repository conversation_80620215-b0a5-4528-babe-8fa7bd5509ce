package services

import (
	"PrintEaseBackend/internal/auth"
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"errors"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct{}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(id int64) (*models.User, error) {
	var user models.User
	result := database.DB.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &user, nil
}

// GetUserByIdentity 根据标识获取用户
func (s *UserService) GetUserByIdentity(identityType, identifier string) (*models.User, error) {
	var auth models.UserAuth
	result := database.DB.Where("identity_type = ? AND identifier = ?", identityType, identifier).First(&auth)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}

	return s.GetUserByID(auth.UserID)
}

// Login 用户登录
func (s *UserService) Login(req *models.UserLoginReq, ip string) (*models.UserLoginResp, error) {
	// 1. 查找用户认证信息
	user, err := s.GetUserByIdentity(req.IdentityType, req.Identifier)
	if err != nil {
		return nil, err
	}

	var userID int64
	if user == nil {
		// 新用户，创建用户记录
		user = &models.User{
			Status: 1, // 正常状态
		}
		if err := database.DB.Create(user).Error; err != nil {
			return nil, err
		}
		userID = user.ID

		// 创建认证记录
		auth := &models.UserAuth{
			UserID:       userID,
			IdentityType: req.IdentityType,
			Identifier:   req.Identifier,
			Verified:     true,
		}

		// 如果是密码登录，需要加密密码
		if req.IdentityType == "web" || req.IdentityType == "client" {
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Credential), bcrypt.DefaultCost)
			if err != nil {
				return nil, err
			}
			auth.Credential = string(hashedPassword)
		}

		if err := database.DB.Create(auth).Error; err != nil {
			return nil, err
		}
	} else {
		userID = user.ID
		// 如果是密码登录，验证密码
		if req.IdentityType == "web" || req.IdentityType == "client" {
			var auth models.UserAuth
			if err := database.DB.Where("user_id = ? AND identity_type = ?", userID, req.IdentityType).First(&auth).Error; err != nil {
				return nil, err
			}
			if err := bcrypt.CompareHashAndPassword([]byte(auth.Credential), []byte(req.Credential)); err != nil {
				return nil, errors.New("账号或密码错误")
			}
		}
	}

	// 2. 生成token
	jwtManager := auth.NewJWTManager(config.GlobalConfig.Auth)
	token, err := jwtManager.GenerateToken(
		userID,
		"", // 用户名
		0,  // 商店ID
		"", // 商店名称
		auth.ClientType(req.ClientType),
		auth.RoleUser,
	)
	if err != nil {
		return nil, err
	}

	// 3. 更新用户最后登录信息
	now := time.Now()
	updates := map[string]interface{}{
		"last_login_time": now,
		"last_login_ip":   ip,
	}
	if err := database.DB.Model(&models.User{}).Where("id = ?", userID).Updates(updates).Error; err != nil {
		return nil, err
	}

	// 4. 构造响应
	return &models.UserLoginResp{
		Token: token,
		User:  user,
		ExpiresIn: int64(jwtManager.GetExpirationTime(
			auth.ClientType(req.ClientType),
			auth.RoleUser,
		).Seconds()),
	}, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(id int64, req *models.UserUpdateReq) error {
	updates := make(map[string]interface{})
	if req.Nickname != nil {
		updates["nickname"] = *req.Nickname
	}
	if req.AvatarURL != nil {
		updates["avatar_url"] = *req.AvatarURL
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}

	return database.DB.Model(&models.User{}).Where("id = ?", id).Updates(updates).Error
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(id int64, req *models.UserChangePasswordReq) error {
	var auth models.UserAuth
	if err := database.DB.Where("user_id = ? AND identity_type IN ('web', 'client')", id).First(&auth).Error; err != nil {
		return err
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(auth.Credential), []byte(req.OldPassword)); err != nil {
		return errors.New("旧密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	return database.DB.Model(&models.UserAuth{}).Where("id = ?", auth.ID).Update("credential", string(hashedPassword)).Error
}
