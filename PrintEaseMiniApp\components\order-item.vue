<template>
  <view class="order-item" @tap="goToDetail">
    <view class="order-header">
      <view class="store-info">
        <text class="store-name">{{order.store ? order.store.name : '未知商家'}}</text>
      </view>
      <view class="order-status" :class="'status-' + order.status">
        <text>{{getStatusText(order.status)}}</text>
      </view>
    </view>
    
    <view class="order-content">
      <view class="file-list">
        <view 
          class="file-item"
          v-for="(item, itemIndex) in order.order_items.slice(0, 3)" 
          :key="item.id"
        >
          <image 
            class="file-icon" 
            :src="getFileIcon(item.cart_item.file.file_type)" 
            mode="aspectFit"
          ></image>
          <view class="file-info">
            <view class="file-name">{{item.cart_item.file.filename}}</view>
            <view class="file-pages">{{item.cart_item.file.page_count}}页</view>
          </view>
        </view>
        
        <view class="file-more" v-if="order.order_items.length > 3">
          <text>等{{order.order_items.length}}个文件</text>
        </view>
      </view>
    </view>
    
    <view class="order-footer">
      <view class="order-time">{{formatTime(order.created_at)}}</view>
      <view class="order-price">
        <text>￥</text>
        <text class="price-value">{{order.total_fee.toFixed(2)}}</text>
      </view>
    </view>
    
    <view class="order-actions">
      <button 
        class="action-btn pay-btn" 
        v-if="order.status === 0"
        @tap.stop="payOrder"
      >立即支付</button>
      <button 
        class="action-btn cancel-btn" 
        v-if="order.status === 0"
        @tap.stop="cancelOrder"
      >取消订单</button>
    </view>
  </view>
</template>

<script>
import { getFileIcon } from '@/utils/file';

export default {
  name: 'OrderItem',
  props: {
    order: {
      type: Object,
      required: true
    }
  },
  methods: {
    getFileIcon(fileType) {
      return getFileIcon(fileType);
    },
    
    getStatusText(status) {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '打印中',
        3: '已完成',
        4: '已取消',
        5: '已退款'
      };
      return statusMap[status] || '未知状态';
    },
    
    formatTime(timeStr) {
      if (!timeStr) return '';
      const date = new Date(timeStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },
    
    goToDetail() {
      console.log('跳转到订单详情:', this.order.id);
      this.$emit('detail', this.order.id);
    },
    
    payOrder() {
      console.log('支付订单:', this.order.id);
      this.$emit('pay', this.order.id);
    },
    
    cancelOrder() {
      console.log('取消订单:', this.order.id);
      this.$emit('cancel', this.order.id);
    }
  }
}
</script>

<style lang="scss">
.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .store-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
  }
  
  .order-status {
    font-size: 26rpx;
    
    &.status-0 {
      color: #ff9800;
    }
    
    &.status-1, &.status-2 {
      color: #1aad19;
    }
    
    &.status-3 {
      color: #8e8e8e;
    }
    
    &.status-4, &.status-5 {
      color: #999;
    }
  }
}

.order-content {
  margin-bottom: 20rpx;
  
  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      padding: 15rpx 15rpx;
      border-bottom: 1rpx solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .file-icon {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
      }
      
      .file-info {
        flex: 1;
        
        .file-name {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
          white-space: normal;
          word-break: break-all;
          overflow: visible;
        }
        
        .file-pages {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
    
    .file-more {
      font-size: 24rpx;
      color: #999;
      padding: 10rpx 0;
      text-align: center;
    }
  }
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
  
  .order-time {
    font-size: 24rpx;
    color: #999;
  }
  
  .order-price {
    font-size: 26rpx;
    color: #ff6b00;
    
    .price-value {
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30rpx;
  
  .action-btn {
    padding: 0 30rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 10rpx;
    font-size: 24rpx;
    margin-left: 10rpx;
    margin-right: 10rpx;
    background-color: #fff;
  }

  .pay-btn {
    background-color: #1aad19;
    color: white;
  }

  .cancel-btn {
    border: 1rpx solid #ccc;
    color: #666;
  }
}
</style> 