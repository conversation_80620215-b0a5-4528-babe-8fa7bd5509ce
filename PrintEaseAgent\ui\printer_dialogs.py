import json
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                           QLineEdit, QComboBox, QPushButton, QMessageBox,
                           QFormLayout, QGroupBox, QCheckBox)
from PyQt5.QtCore import Qt
import win32print

class AddPrinterDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加打印机")
        self.setModal(True)
        self.resize(500, 400)

        # 获取数据库管理器实例
        from core.database import PrinterManager
        self.printer_manager_db = PrinterManager()

        # 创建主布局
        layout = QVBoxLayout(self)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 打印机名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入打印机显示名称")
        basic_layout.addRow("打印机名称:", self.name_edit)

        # 系统打印机选择
        self.system_printer_combo = QComboBox()
        self.refresh_system_printers()  # 先刷新列表
        self.system_printer_combo.currentTextChanged.connect(self.on_system_printer_changed)  # 后连接信号
        basic_layout.addRow("系统打印机:", self.system_printer_combo)

        # 刷新系统打印机按钮
        refresh_btn = QPushButton("刷新系统打印机列表")
        refresh_btn.clicked.connect(self.refresh_system_printers)
        basic_layout.addRow("", refresh_btn)

        layout.addWidget(basic_group)

        # 打印机配置组
        config_group = QGroupBox("打印机配置")
        config_layout = QFormLayout(config_group)

        # 默认纸张大小
        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItems(["A4", "A3", "B4", "B5", "Letter"])
        config_layout.addRow("默认纸张:", self.paper_size_combo)

        # 是否支持彩色打印
        self.color_check = QCheckBox("支持彩色打印")
        config_layout.addRow("打印模式:", self.color_check)

        # 是否支持双面打印
        self.duplex_check = QCheckBox("支持双面打印")
        config_layout.addRow("双面打印:", self.duplex_check)

        layout.addWidget(config_group)

        # 按钮组
        button_layout = QHBoxLayout()

        self.save_btn = QPushButton("保存")
        self.save_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.save_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def on_system_printer_changed(self, system_name):
        """处理系统打印机选择变化"""
        if not system_name:
            return

        # 如果是新打印机，自动填充显示名称
        if not self.name_edit.text():
            # 移除可能的"（已添加）"标记
            clean_name = system_name.replace("（已添加）", "").strip()
            self.name_edit.setText(clean_name)

    def refresh_system_printers(self):
        """刷新系统打印机列表"""
        try:
            current_printer = self.system_printer_combo.currentText()
            if "（已添加）" in current_printer:
                current_printer = current_printer.replace("（已添加）", "").strip()

            self.system_printer_combo.clear()

            # 获取系统打印机列表
            printers = [printer[2] for printer in win32print.EnumPrinters(win32print.PRINTER_ENUM_LOCAL, None, 1)]

            # 获取已添加的打印机列表
            added_printers = []
            for printer_name in printers:
                if self.printer_manager_db.get_printer_by_system_name(printer_name, 'windows'):
                    added_printers.append(printer_name)

            # 添加到下拉框，标记已添加的打印机
            for printer_name in printers:
                display_name = f"{printer_name}（已添加）" if printer_name in added_printers else printer_name
                self.system_printer_combo.addItem(display_name)

            # 恢复之前选择的打印机（如果还存在）
            if current_printer:
                # 查找原始名称或带标记的名称
                index = self.system_printer_combo.findText(current_printer)
                if index < 0:
                    index = self.system_printer_combo.findText(f"{current_printer}（已添加）")
                if index >= 0:
                    self.system_printer_combo.setCurrentIndex(index)

        except Exception as e:
            QMessageBox.warning(self, "错误", f"获取系统打印机列表失败: {str(e)}")

    def get_printer_data(self):
        """获取打印机数据"""
        name = self.name_edit.text().strip()
        system_name = self.system_printer_combo.currentText()

        # 移除可能的"（已添加）"标记
        system_name = system_name.replace("（已添加）", "").strip()

        if not name:
            QMessageBox.warning(self, "错误", "请输入打印机名称")
            return None

        if not system_name:
            QMessageBox.warning(self, "错误", "请选择系统打印机")
            return None

        # 检查打印机是否已存在
        existing_printer = self.printer_manager_db.get_printer_by_system_name(system_name, 'windows')
        if existing_printer:
            QMessageBox.warning(
                self,
                "打印机已存在",
                f"该打印机已添加到系统中！\n显示名称为：{existing_printer['name']}"
            )
            return None

        # 生成打印机配置
        config = {
            "default_settings": {
                "paper_size": self.paper_size_combo.currentText(),
                "color_mode": "color" if self.color_check.isChecked() else "black",
                "duplex": self.duplex_check.isChecked()
            },
            "supported_features": {
                "color": self.color_check.isChecked(),
                "duplex": self.duplex_check.isChecked(),
                "paper_sizes": ["A4", "A3", "B4", "B5", "Letter"]
            }
        }

        return {
            "name": name,
            "system_name": system_name,
            "config": json.dumps(config)
        }

class EditPrinterDialog(QDialog):
    def __init__(self, printer_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("编辑打印机")
        self.setModal(True)
        self.resize(500, 400)

        # 保存原始打印机数据
        self.printer_data = printer_data
        self.config = json.loads(printer_data['config']) if printer_data['config'] else {}

        # 创建主布局
        layout = QVBoxLayout(self)

        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout(basic_group)

        # 打印机名称
        self.name_edit = QLineEdit(printer_data['name'])
        basic_layout.addRow("打印机名称:", self.name_edit)

        # 系统打印机名称（只读）
        system_name_label = QLabel(printer_data['system_name'])
        basic_layout.addRow("系统打印机:", system_name_label)

        layout.addWidget(basic_group)

        # 打印机配置组
        config_group = QGroupBox("打印机配置")
        config_layout = QFormLayout(config_group)

        # 默认纸张大小
        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItems(["A4", "A3", "B4", "B5", "Letter"])
        current_paper = self.config.get('default_settings', {}).get('paper_size', 'A4')
        self.paper_size_combo.setCurrentText(current_paper)
        config_layout.addRow("默认纸张:", self.paper_size_combo)

        # 是否支持彩色打印
        self.color_check = QCheckBox("支持彩色打印")
        self.color_check.setChecked(
            self.config.get('supported_features', {}).get('color', False)
        )
        config_layout.addRow("打印模式:", self.color_check)

        # 是否支持双面打印
        self.duplex_check = QCheckBox("支持双面打印")
        self.duplex_check.setChecked(
            self.config.get('supported_features', {}).get('duplex', False)
        )
        config_layout.addRow("双面打印:", self.duplex_check)

        layout.addWidget(config_group)

        # 按钮组
        button_layout = QHBoxLayout()

        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.accept)
        button_layout.addWidget(save_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def get_printer_data(self):
        """获取打印机数据"""
        name = self.name_edit.text().strip()

        if not name:
            QMessageBox.warning(self, "错误", "请输入打印机名称")
            return None

        # 更新打印机配置
        self.config['default_settings'] = {
            "paper_size": self.paper_size_combo.currentText(),
            "color_mode": "color" if self.color_check.isChecked() else "black",
            "duplex": self.duplex_check.isChecked()
        }
        self.config['supported_features'] = {
            "color": self.color_check.isChecked(),
            "duplex": self.duplex_check.isChecked(),
            "paper_sizes": ["A4", "A3", "B4", "B5", "Letter"]
        }

        return {
            "name": name,
            "config": json.dumps(self.config)
        }

class ConfigurePrinterDialog(QDialog):
    def __init__(self, printer_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("打印机配置")
        self.setModal(True)
        self.resize(500, 400)

        # 保存原始打印机数据
        self.printer_data = printer_data
        self.config = json.loads(printer_data['config']) if printer_data['config'] else {}

        # 创建主布局
        layout = QVBoxLayout(self)

        # 打印机信息组
        info_group = QGroupBox("打印机信息")
        info_layout = QFormLayout(info_group)

        info_layout.addRow("打印机名称:", QLabel(printer_data['name']))
        info_layout.addRow("系统名称:", QLabel(printer_data['system_name']))

        layout.addWidget(info_group)

        # 默认设置组
        default_group = QGroupBox("默认打印设置")
        default_layout = QFormLayout(default_group)

        # 默认纸张大小
        self.paper_size_combo = QComboBox()
        self.paper_size_combo.addItems(["A4", "A3", "B4", "B5", "Letter"])
        current_paper = self.config.get('default_settings', {}).get('paper_size', 'A4')
        self.paper_size_combo.setCurrentText(current_paper)
        default_layout.addRow("默认纸张:", self.paper_size_combo)

        # 默认打印模式
        self.color_mode_combo = QComboBox()
        self.color_mode_combo.addItems(["黑白", "彩色"])
        current_mode = "彩色" if self.config.get('default_settings', {}).get('color_mode') == 'color' else "黑白"
        self.color_mode_combo.setCurrentText(current_mode)
        default_layout.addRow("默认模式:", self.color_mode_combo)

        # 默认双面打印
        self.duplex_check = QCheckBox()
        self.duplex_check.setChecked(
            self.config.get('default_settings', {}).get('duplex', False)
        )
        default_layout.addRow("默认双面:", self.duplex_check)

        # 默认打印质量
        self.quality_combo = QComboBox()
        self.quality_combo.addItems(["草稿", "正常", "高质量"])
        current_quality = self.config.get('default_settings', {}).get('quality', '正常')
        self.quality_combo.setCurrentText(current_quality)
        default_layout.addRow("打印质量:", self.quality_combo)

        layout.addWidget(default_group)

        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QFormLayout(advanced_group)

        # 打印超时设置
        self.timeout_edit = QLineEdit()
        self.timeout_edit.setText(str(self.config.get('advanced_settings', {}).get('timeout', 300)))
        self.timeout_edit.setPlaceholderText("单位：秒")
        advanced_layout.addRow("打印超时:", self.timeout_edit)

        # 错误重试次数
        self.retry_edit = QLineEdit()
        self.retry_edit.setText(str(self.config.get('advanced_settings', {}).get('max_retries', 3)))
        advanced_layout.addRow("错误重试:", self.retry_edit)

        layout.addWidget(advanced_group)

        # 按钮组
        button_layout = QHBoxLayout()

        save_btn = QPushButton("保存")
        save_btn.clicked.connect(self.accept)
        button_layout.addWidget(save_btn)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        layout.addLayout(button_layout)

    def get_printer_data(self):
        """获取打印机配置数据"""
        try:
            timeout = int(self.timeout_edit.text())
            max_retries = int(self.retry_edit.text())
        except ValueError:
            QMessageBox.warning(self, "错误", "超时时间和重试次数必须是整数")
            return None

        # 更新打印机配置
        self.config['default_settings'] = {
            "paper_size": self.paper_size_combo.currentText(),
            "color_mode": "color" if self.color_mode_combo.currentText() == "彩色" else "black",
            "duplex": self.duplex_check.isChecked(),
            "quality": self.quality_combo.currentText()
        }

        self.config['advanced_settings'] = {
            "timeout": timeout,
            "max_retries": max_retries
        }

        return {
            "config": json.dumps(self.config)
        }