import sqlite3
import os
import logging
from typing import Optional, Dict, Any, List
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)

class BaseManager:
    """数据库管理基类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config_manager = ConfigManager()
        self.db_path = self.config_manager.get_database_path()
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.connection: Optional[sqlite3.Connection] = None
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        try:
            self.connect()
            self._create_tables()
            logger.info(f"{self.__class__.__name__} 数据库初始化成功")
        except Exception as e:
            logger.error(f"{self.__class__.__name__} 数据库初始化失败: {str(e)}")
            raise
        finally:
            self.close()
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def _create_tables(self):
        """创建数据库表（由子类实现）"""
        raise NotImplementedError("子类必须实现_create_tables方法") 