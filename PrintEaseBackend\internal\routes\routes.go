package routes

import (
	"PrintEaseBackend/internal/auth"
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/handlers"
	"PrintEaseBackend/internal/middleware"

	"github.com/gin-gonic/gin"
)

func InitRoutes(r *gin.Engine) {
	// API 路由组
	api := r.Group("/api")
	{
		// 公开接口
		public := api.Group("")
		{
			// 用户相关接口
			userHandler := handlers.NewUserHandler()
			users := public.Group("/users")
			{
				users.POST("/login", userHandler.Login)
			}

			// 商家登录
			merchantHandler := handlers.NewMerchantHandler()
			merchants := public.Group("/merchants")
			{
				merchants.POST("/login", merchantHandler.Login)
			}

			// 店铺列表
			stores := public.Group("/stores")
			{
				stores.GET("", handlers.GetStores)
			}

			// 文件上传和下载（小程序暂不认证）
			public.POST("/upload", handlers.UploadHandler)
			public.GET("/download", handlers.DownloadHandler)

			// 购物车相关（小程序暂不认证）
			cartHandler := handlers.NewCartHandler()
			cartItems := public.Group("/cart-items")
			{
				cartItems.GET("", cartHandler.GetCartItems)
				cartItems.PUT("/:id/status", cartHandler.UpdateCartItemStatus)
				cartItems.PUT("/batch-status", cartHandler.BatchUpdateCartStatus)
				cartItems.PUT("/:id", cartHandler.UpdateCartItemSettings)
			}

			// 订单相关（小程序暂不认证）
			orderHandler := handlers.NewOrderHandler()
			orders := public.Group("/orders")
			{
				orders.POST("", orderHandler.CreateOrder)
				orders.GET("", orderHandler.GetOrderList)
				orders.GET("/:id", orderHandler.GetOrderDetail)
				orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
				orders.POST("/:id/print", orderHandler.PrintOrder) // 新增打印订单接口
			}
		}

		// 需要认证的接口（商家和代理程序）
		authenticated := api.Group("")
		authenticated.Use(middleware.Auth())
		{
			// 用户认证接口
			userHandler := handlers.NewUserHandler()
			userAuth := authenticated.Group("/users")
			userAuth.Use(middleware.RequireRoles(auth.RoleUser))
			{
				userAuth.GET("/info", userHandler.GetUserInfo)
				userAuth.PUT("/info", userHandler.UpdateUser)
				userAuth.POST("/change-password", userHandler.ChangePassword)
			}

			// 商家认证接口
			merchantHandler := handlers.NewMerchantHandler()

			// 令牌验证接口（单独的路由组）
			tokenAuth := authenticated.Group("/token")
			tokenAuth.Use(middleware.RequireRoles(auth.RoleMerchantOwner, auth.RoleMerchantStaff))
			{
				tokenAuth.GET("/verify", merchantHandler.VerifyToken)
			}

			// 商家管理接口
			merchantAuth := authenticated.Group("/merchants")
			merchantAuth.Use(middleware.RequireRoles(auth.RoleMerchantOwner, auth.RoleMerchantStaff))
			{
				// 所有商家都可以访问的接口
				merchantAuth.POST("/change-password", merchantHandler.ChangePassword)
				merchantAuth.GET("/:id", merchantHandler.GetMerchant)

				// 只有店主可以访问的接口
				ownerOnly := merchantAuth.Group("")
				ownerOnly.Use(middleware.RequireRoles(auth.RoleMerchantOwner))
				{
					ownerOnly.POST("", merchantHandler.CreateMerchant)
					ownerOnly.PUT("/:id", merchantHandler.UpdateMerchant)
					ownerOnly.GET("", merchantHandler.ListMerchants)
					ownerOnly.DELETE("/:id", merchantHandler.DeleteMerchant)
				}
			}

			// 代理程序专用接口
			agent := authenticated.Group("/agent")
			agent.Use(middleware.RequireClientTypes(auth.ClientTypeAgent))
			{
				// 如果有代理程序专用的接口，放在这里
			}
		}
	}

	// WebSocket路由（需要认证）
	wsHandler := handlers.NewWebSocketHandler()
	r.GET("/ws/print-agent", middleware.Auth(), wsHandler.HandleConnection)

	// 静态文件服务
	r.Static("/uploads", "./"+config.GlobalConfig.Upload.UploadDir)
}
