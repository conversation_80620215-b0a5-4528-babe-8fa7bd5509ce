import request from '@/utils/request'
import config from '@/config/config.js'

// 上传文件
export const uploadFile = (filePath, data = {}) => {
	return new Promise((resolve, reject) => {
		console.log('开始上传文件，使用基础URL:', config.api.baseUrl);
		console.log('上传文件参数:', {
			filePath: filePath,
			formData: data
		});

		uni.uploadFile({
			url: config.api.baseUrl + '/api/upload',
			filePath: filePath,
			name: 'file',
			formData: data,  // 这里已经包含了传入的filename
			header: {
				'token': uni.getStorageSync('token') || '',
			},
			success: (uploadFileRes) => {
				console.log('上传结果:', uploadFileRes);
				if (uploadFileRes.statusCode === 200) {
					try {
						const result = JSON.parse(uploadFileRes.data);
						if (result.code === 0) {
							resolve(result);
						} else {
							reject(new Error(result.message || '上传失败'));
						}
					} catch (e) {
						reject(new Error('解析响应数据失败'));
					}
				} else {
					reject(new Error(`上传失败，状态码：${uploadFileRes.statusCode}`));
				}
			},
			fail: (err) => {
				console.error('上传失败:', err);
				reject(new Error(err.errMsg || '上传失败'));
			}
		});
	})
}

// 下载文件
export const downloadFile = (fileId) => {
	return new Promise((resolve, reject) => {
		uni.downloadFile({
			url: config.api.baseUrl + '/api/download?file_id=' + fileId,
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res.tempFilePath)
				} else {
					reject(new Error('下载失败'))
				}
			},
			fail: (err) => {
				reject(err)
			}
		})
	})
}

// 获取店铺列表
export const getStoreList = (data) => {
	console.log('请求店铺列表参数:', data)
	return request.get('/api/stores', data) // 使用request.get方法
}

// 创建订单
export const createOrder = (data) => {
	return request.post('/api/orders', data);  // 使用request.post方法
};

// 打印订单
export const printOrder = (orderId, userId) => {
	console.log('打印订单参数:', { orderId, userId });
	return request.post(`/api/orders/${orderId}/print`, { user_id: userId });
};

// 获取订单列表
export const getOrderList = (userId, status, page = 1, pageSize = 10) => {
	console.log('获取订单列表参数:', { userId, status, page, pageSize })
	const params = {
		user_id: userId,
		page: page,
		page_size: pageSize
	}

	if (status !== undefined && status !== null) {
		params.status = status
	}

	return request.get('/api/orders', params)
}

// 获取订单详情
export const getOrderDetail = (orderId, userId) => {
	console.log('获取订单详情参数:', { orderId, userId })
	return request.get(`/api/orders/${orderId}`, { user_id: userId })
}

// 更新订单状态
export const updateOrderStatus = (orderId, userId, status) => {
	console.log('更新订单状态参数:', { orderId, userId, status })
	return request.put(`/api/orders/${orderId}/status`, { user_id: userId, status: status })
}

// 获取购物车列表
export const getCartItems = (userId) => {
	return request.get('/api/cart-items', {
		user_id: userId
	})
}

// 更新单个购物车项状态
export const updateCartItemStatus = (id, status) => {
	return request.put(`/api/cart-items/${id}/status`, {
		status: status
	})
}

// 批量更新购物车项状态
export const batchUpdateCartStatus = (ids, status) => {
	return request.put('/api/cart-items/batch-status', {
		ids: ids,
		status: status
	})
}

/**
 * 更新购物车项目的打印设置
 * @param {Number} cartItemId 购物车项目ID
 * @param {Object} settings 打印设置参数
 * @returns {Promise}
 */
export function updateCartItemSettings(cartItemId, settings) {
	return new Promise((resolve, reject) => {
		uni.request({
			url: `${config.api.baseUrl}/api/cart-items/${cartItemId}`,
			method: 'PUT',
			data: settings,
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res.data);
				} else {
					reject(new Error(res.data.message || '更新设置失败'));
				}
			},
			fail: (err) => {
				console.error('更新购物车项目设置失败:', err);
				reject(err);
			}
		});
	});
}