{"name": "PrintEase云打印", "appid": "", "description": "自助打印小程序", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>"]}, "ios": {"dSYMs": false}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false, "es6": true, "postcss": true, "minified": true, "hotReload": false, "hot": false}, "usingComponents": true, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "你的位置信息将用于查找附近的打印店"}}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "mp-qq": {"appid": "你的QQ小程序appid", "setting": {"urlCheck": false}, "usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3"}