package models

import "time"

// UserSession 用户会话表
type UserSession struct {
	ID         int64     `gorm:"primarykey" json:"id"`
	UserID     int64     `gorm:"not null;index" json:"user_id"`         // 用户ID
	Token      string    `gorm:"size:255;not null;unique" json:"token"` // 会话token
	DeviceType string    `gorm:"size:20;not null" json:"device_type"`   // 设备类型：wx_mini/qq_mini/web/client
	DeviceInfo string    `gorm:"size:255" json:"device_info"`           // 设备信息
	ExpiredAt  time.Time `json:"expired_at"`                            // 过期时间
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`

	User User `gorm:"foreignKey:UserID" json:"user"` // 关联用户模型
}

func (UserSession) TableName() string {
	return "user_sessions"
}

// Indexes gorm 索引设置
func (UserSession) Indexes() [][]string {
	return [][]string{
		{"user_id", "device_type"},
		{"token"},
	}
}
