package handlers

import (
	"PrintEaseBackend/internal/models"
	"PrintEaseBackend/internal/services"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// MerchantHandler 商家处理器
type MerchantHandler struct {
	merchantService *services.MerchantService
}

// NewMerchantHandler 创建商家处理器
func NewMerchantHandler() *MerchantHandler {
	return &MerchantHandler{
		merchantService: &services.MerchantService{},
	}
}

// CreateMerchant 创建商家
func (h *MerchantHandler) CreateMerchant(c *gin.Context) {
	var req models.MerchantCreateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}

	merchant, err := h.merchantService.CreateMerchant(&req)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "创建商家失败",
			"err":  err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": merchant,
	})
}

// UpdateMerchant 更新商家
func (h *MerchantHandler) UpdateMerchant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的商家ID",
			"err":  err.Error(),
		})
		return
	}

	var req models.MerchantUpdateReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}

	err = h.merchantService.UpdateMerchant(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "更新商家失败",
			"err":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
	})
}

// GetMerchant 获取商家信息
func (h *MerchantHandler) GetMerchant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的商家ID",
			"err":  err.Error(),
		})
		return
	}

	merchant, err := h.merchantService.GetMerchantByID(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "获取商家失败",
			"err":  err.Error(),
		})
		return
	}

	if merchant == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code": -1,
			"msg":  "商家不存在",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": merchant,
	})
}

// ListMerchants 获取商家列表
func (h *MerchantHandler) ListMerchants(c *gin.Context) {
	var query models.MerchantQueryReq
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}

	merchants, total, err := h.merchantService.ListMerchants(&query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "获取商家列表失败",
			"err":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": gin.H{
			"list":      merchants,
			"total":     total,
			"page":      query.Page,
			"page_size": query.PageSize,
		},
	})
}

// DeleteMerchant 删除商家
func (h *MerchantHandler) DeleteMerchant(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的商家ID",
			"err":  err.Error(),
		})
		return
	}

	err = h.merchantService.DeleteMerchant(id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "删除商家失败",
			"err":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
	})
}

// Login 商家登录
func (h *MerchantHandler) Login(c *gin.Context) {
	var req models.MerchantLoginReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}

	// 获取客户端IP
	clientIP := c.ClientIP()

	resp, err := h.merchantService.Login(&req, clientIP)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  err.Error(),
			"err":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": resp,
	})
}

// ChangePassword 修改密码
func (h *MerchantHandler) ChangePassword(c *gin.Context) {
	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  "未授权",
		})
		return
	}

	id := userID.(int64)

	var req models.MerchantChangePasswordReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "参数错误",
			"err":  err.Error(),
		})
		return
	}

	err := h.merchantService.ChangePassword(id, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "修改密码失败",
			"err":  err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
	})
}

// VerifyToken 验证令牌有效性
func (h *MerchantHandler) VerifyToken(c *gin.Context) {
	// 从上下文获取认证信息（由Auth中间件注入）
	claims, exists := c.Get("claims")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code": -1,
			"msg":  "未授权",
		})
		return
	}

	// 返回令牌验证成功
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "令牌有效",
		"data": claims,
	})
}
