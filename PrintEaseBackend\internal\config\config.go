package config

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/spf13/viper"
)

// Config 配置结构体
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	Auth     AuthConfig     `mapstructure:"auth"`
	Store    StoreConfig    `mapstructure:"store"`
	Upload   UploadConfig   `mapstructure:"upload"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Host    string `mapstructure:"host"`
	Port    int    `mapstructure:"port"`
	Mode    string `mapstructure:"mode"`
	Timeout int    `mapstructure:"timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Driver          string `mapstructure:"driver"`
	Host            string `mapstructure:"host"`
	Port            int    `mapstructure:"port"`
	Username        string `mapstructure:"username"`
	Password        string `mapstructure:"password"`
	DBName          string `mapstructure:"dbname"`
	Charset         string `mapstructure:"charset"`
	ParseTime       bool   `mapstructure:"parse_time"`
	Loc             string `mapstructure:"loc"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	ConnMaxLifetime int    `mapstructure:"conn_max_lifetime"`
}

// AuthConfig 认证配置
type AuthConfig struct {
	JWT JWTConfig `mapstructure:"jwt"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	SecretKey       string                `mapstructure:"secret_key"`
	ExpirationTimes ExpirationTimesConfig `mapstructure:"expiration_times"`
	RefreshTimes    RefreshTimesConfig    `mapstructure:"refresh_times"`
}

// ExpirationTimesConfig 过期时间配置
type ExpirationTimesConfig struct {
	Merchant MerchantTimesConfig `mapstructure:"merchant"`
}

// RefreshTimesConfig 刷新时间配置
type RefreshTimesConfig struct {
	Merchant MerchantTimesConfig `mapstructure:"merchant"`
}

// MerchantTimesConfig 商家时间配置
type MerchantTimesConfig struct {
	Client int `mapstructure:"client"` // 客户端
	Web    int `mapstructure:"web"`    // 网页
	Agent  int `mapstructure:"agent"`  // 代理程序
}

// StoreConfig 商店配置
type StoreConfig struct {
	MaxDistance float64 `mapstructure:"maxDistance"`
}

// UploadConfig 上传配置
type UploadConfig struct {
	UploadDir   string `mapstructure:"uploadDir"`
	MaxFileSize int    `mapstructure:"maxFileSize"`
}

var GlobalConfig Config

// LoadConfig 加载配置
func LoadConfig() error {
	// 获取环境变量，默认为开发环境
	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "dev"
	}

	v := viper.New()

	// 设置配置文件路径
	configPath := filepath.Join("internal", "config")
	v.AddConfigPath(configPath)
	v.SetConfigName(fmt.Sprintf("config.%s", env))
	v.SetConfigType("yaml")

	// 读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置到结构体
	if err := v.Unmarshal(&GlobalConfig); err != nil {
		return fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 从环境变量覆盖敏感配置（如果有）
	if dbPassword := os.Getenv("DB_PASSWORD"); dbPassword != "" {
		GlobalConfig.Database.Password = dbPassword
	}

	// 从环境变量注入JWT密钥
	if jwtSecretKey := os.Getenv("JWT_SECRET_KEY"); jwtSecretKey != "" {
		GlobalConfig.Auth.JWT.SecretKey = jwtSecretKey
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%v&loc=%s",
		c.Username,
		c.Password,
		c.Host,
		c.Port,
		c.DBName,
		c.Charset,
		c.ParseTime,
		c.Loc,
	)
}
