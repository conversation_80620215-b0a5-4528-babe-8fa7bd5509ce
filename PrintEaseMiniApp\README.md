# PrintEaseMiniApp 项目说明文档

## 项目介绍
PrintEaseMiniApp 是 PrintEase 自助打印系统的小程序端，采用 uni-app 框架开发，可同时适配微信小程序和 QQ 小程序。该小程序提供用户友好的界面，让用户可以方便地选择打印店、上传文件、设置打印参数、支付订单并查看订单状态。

## 技术栈
- 前端框架：uni-app
- 状态管理：Vuex 4
- UI组件：uni-ui
- 样式处理：SCSS
- 构建工具：HBuilderX

## 项目结构
```
PrintEaseMiniApp/
├── api/                  # API接口封装
├── components/           # 公共组件
├── config/               # 全局配置
├── pages/                # 页面文件
│   ├── index/            # 首页
│   ├── stores/           # 选择打印店页面
│   ├── cart/             # 购物车页面
│   ├── order/            # 订单列表页面
│   ├── order_detail/     # 订单详情页面
│   └── mine/             # 个人中心页面
├── static/               # 静态资源
│   └── icons/            # 图标资源
├── store/                # Vuex状态管理
├── utils/                # 工具函数
├── unpackage/            # 编译输出目录(不需要版本控制)
├── App.vue               # 应用入口组件
├── main.js               # 应用入口JS
├── pages.json            # 页面路由配置
├── manifest.json         # 应用配置
└── uni.scss              # 全局样式变量
```

## 功能模块

### 1. 打印店选择
- 基于地理位置显示附近打印店
- 支持地图和列表两种查看方式
- 显示打印店详细信息（名称、地址、营业时间、距离等）

### 2. 文件上传
- 支持从微信聊天记录选择文件
- 支持从本地相册选择图片
- 支持拍照上传

### 3. 打印设置
- 支持设置打印参数：
  - 份数
  - 布局（纵向，横向）
  - 页面范围（全部，指定页码）
  - 纸张（A3，A4，A5等）
  - 颜色（黑白，彩色）
  - 单双面
  - 每张纸打印的页数（1，2，4，6，9，16）

### 4. 订单管理
- 订单创建与支付
- 订单状态查询
- 历史订单查看
- 订单详情查看

### 5. 用户中心
- 用户信息管理
- 常用打印店收藏

## 状态管理
项目使用 Vuex 进行状态管理，主要管理以下状态：
- 用户信息 (userInfo)
- 用户令牌 (token)
- 选中的打印店信息 (selectedStore)

## 环境配置
项目支持开发环境和生产环境的配置：
- 开发环境：使用本地API服务 (http://127.0.0.1:8080)
- 生产环境：使用线上API服务 (https://your-api-domain.com)

## 开发指南

### 环境准备
1. 安装 Node.js (推荐 v14 或更高版本)
2. 安装 HBuilderX 编辑器
3. 微信开发者工具 (用于调试微信小程序)

### 安装依赖
```bash
npm install
```

### 开发调试
1. 使用 HBuilderX 打开项目
2. 点击"运行"→"运行到小程序模拟器"→"微信开发者工具"
3. 或者使用命令行：
```bash
npm run dev
```

### 生产构建
```bash
npm run build
```

## 技术实现说明

### 文件上传流程
1. 用户选择文件
2. 前端调用 uploadFile API 上传文件到服务器
3. 服务器处理文件并返回文件ID
4. 前端将文件添加到购物车

### 打印店选择
1. 获取用户位置
2. 调用 getStoreList API 获取附近打印店
3. 在地图和列表中展示打印店信息
4. 用户选择打印店后，保存到 Vuex store 中

### 订单创建
1. 用户确认购物车中的文件和打印设置
2. 调用 createOrder API 创建订单
3. 跳转到支付页面
4. 支付完成后更新订单状态

## 注意事项
- 文件上传大小限制为 20MB
- 支持的文件类型：doc, docx, xls, xlsx, ppt, pptx, pdf, txt, rtf
- 支持的图片类型：jpg, jpeg, png, gif, bmp, tif, tiff

## 后续开发计划
1. 添加照片冲印功能
2. 添加证件复印功能
3. 支持从百度网盘、金山文档等第三方平台导入文件
4. 添加会员和优惠券功能
5. 实现订单评价功能
6. 优化文件预览体验

## 性能优化
1. 实现文件缓存策略，减少重复下载
2. 使用分包加载优化小程序体积和加载性能
3. 优化图片资源，减少网络请求
4. 实现响应式布局，确保在不同尺寸设备上的良好显示

## 安全性考虑
1. 实现适当的数据安全和用户隐私保护措施
2. 敏感信息加密传输
3. 合理使用本地存储和缓存机制
4. 文件上传安全检查
