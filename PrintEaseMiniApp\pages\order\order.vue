<template>
	<view class="order-container">
		<!-- 订单状态筛选器 -->
		<view class="status-filter">
			<view
				class="filter-item"
				:class="{ active: activeStatus === null }"
				@tap="changeStatus(null)"
			>全部</view>
			<view
				class="filter-item"
				:class="{ active: activeStatus === 0 }"
				@tap="changeStatus(0)"
			>待支付</view>
			<view
				class="filter-item"
				:class="{ active: activeStatus === 1 }"
				@tap="changeStatus(1)"
			>已支付</view>
			<view
				class="filter-item"
				:class="{ active: activeStatus === 2 }"
				@tap="changeStatus(2)"
			>打印中</view>
			<view
				class="filter-item"
				:class="{ active: activeStatus === 3 }"
				@tap="changeStatus(3)"
			>已完成</view>
		</view>

		<!-- 订单列表 -->
		<scroll-view
			class="scroll-container"
			scroll-y="true"
			:style="{ height: scrollViewHeight + 'px' }"
			@scrolltolower="onScrollToLower"
			lower-threshold="50"
			@scroll="onScroll"
			:scroll-top="scrollTop"
			refresher-enabled
			:refresher-triggered="refreshing"
			@refresherrefresh="onRefresh"
			:id="scrollViewId"
		>
			<view class="order-list-inner">
				<view v-if="loading && orders.length === 0" class="loading-box">
					<uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
				</view>

				<view v-else-if="orders.length === 0" class="empty-box">
					<image src="/static/icons/empty-order.png" mode="aspectFit" class="empty-image"></image>
					<text class="empty-text">暂无订单</text>
				</view>

				<view v-else class="order-list">
					<!-- 直接内联订单项 -->
					<view
						class="order-item"
						v-for="order in orders"
						:key="order.id"
						@tap="goToDetail(order.id)"
					>
						<view class="order-header">
							<view class="store-info">
								<text class="store-name">{{order.store ? order.store.name : '未知商家'}}</text>
							</view>
							<view class="order-status" :class="'status-' + order.status">
								<text>{{getStatusText(order.status)}}</text>
							</view>
						</view>

						<view class="order-content">
							<view class="file-list">
								<view
									class="file-item"
									v-for="(item, itemIndex) in order.order_items.slice(0, 3)"
									:key="item.id"
								>
									<image
										class="file-icon"
										:src="getFileIcon(item.cart_item.file.file_type)"
										mode="aspectFit"
									></image>
									<view class="file-info">
										<view class="file-name">{{item.cart_item.file.filename}}</view>
										<view class="file-pages">{{item.cart_item.file.page_count}}页</view>
									</view>
								</view>

								<view class="file-more" v-if="order.order_items.length > 3">
									<text>等{{order.order_items.length}}个文件</text>
								</view>
							</view>
						</view>

						<view class="order-footer">
							<view class="order-time">{{formatTime(order.created_at)}}</view>
							<view class="order-price">
								<text>￥</text>
								<text class="price-value">{{order.total_fee.toFixed(2)}}</text>
							</view>
						</view>

						<view class="order-actions">
							<button
								class="action-btn pay-btn"
								v-if="order.status === 0"
								@tap.stop="payOrder(order.id)"
							>立即支付</button>
							<button
								class="action-btn cancel-btn"
								v-if="order.status === 0"
								@tap.stop="cancelOrder(order.id)"
							>取消订单</button>
						</view>
					</view>

					<!-- 底部加载状态 -->
					<view class="load-more">
						<uni-load-more
							:status="loadingMoreStatus"
							:content-text="loadingText"
						></uni-load-more>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { getOrderList, updateOrderStatus, printOrder } from '@/api/index';
import uniLoadMore from '@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue';
import { getFileIcon } from '@/utils/file';

export default {
	components: {
		uniLoadMore
	},
	data() {
		return {
			activeStatus: null, // 当前选中的状态过滤器，null表示全部
			orders: [], // 订单列表
			loading: true, // 是否正在加载
			loadingMore: false, // 是否正在加载更多
			refreshing: false, // 是否正在下拉刷新
			hasMore: false, // 是否有更多数据
			page: 1, // 当前页码
			pageSize: 10, // 每页数量
			loadingText: {
				contentdown: '上拉显示更多',
				contentrefresh: '正在加载...',
				contentnomore: '没有更多数据了'
			},
			userId: 0, // 用户ID，实际应从用户系统获取
			isDebug: true,
			scrollViewHeight: 0,
			contentHeight: 0,
			scrollTop: 0,
			currentScrollTop: 0,
			scrollViewId: 'order-scroll-view',
			lastLoadedOrderId: null // 记录最后加载的订单ID
		}
	},
	computed: {
		// 计算加载更多的状态
		loadingMoreStatus() {
			if (this.loadingMore) return 'loading';
			if (!this.hasMore) return 'noMore';
			return 'more';
		}
	},
	onLoad() {
		// 从缓存或全局状态获取用户ID
		this.userId = this.getUserId();
		// 加载订单列表
		this.loadOrders();
	},
	methods: {
		// 获取文件图标
		getFileIcon(fileType) {
			return getFileIcon(fileType);
		},

		// 获取用户ID的方法，根据实际登录系统修改
		getUserId() {
			// 这里应该从全局状态或存储中获取用户ID
			// 示例返回固定值
			console.log('获取用户ID');
			return 1; // 测试用，实际应从用户系统获取
		},

		// 加载订单列表
		async loadOrders(reset = true) {
			try {
				// 如果是重置，则显示整体加载状态
				if (reset) {
					this.loading = true;
					this.page = 1;
					// 清空订单列表
					this.orders = [];
				} else {
					// 如果是加载更多，则只显示底部加载状态
					this.loadingMore = true;

					// 记录当前滚动位置
					const currentPosition = this.currentScrollTop;
					console.log('记录当前滚动位置:', currentPosition);

					// 记录最后一个订单ID，用于后续定位
					if (this.orders.length > 0) {
						this.lastLoadedOrderId = this.orders[this.orders.length - 1].id;
						console.log('记录最后加载的订单ID:', this.lastLoadedOrderId);
					}
				}

				console.log('加载订单列表，状态:', this.activeStatus, '页码:', this.page);
				const res = await getOrderList(this.userId, this.activeStatus, this.page, this.pageSize);

				if (res && res.code === 0 && res.data) {
					console.log('订单列表获取成功:', res.data);

					const newOrders = res.data.orders || [];

					if (reset) {
						// 重置时直接替换
						this.orders = newOrders;
					} else {
						// 加载更多时，使用Array.prototype.push.apply追加数据
						// 这种方式比创建新数组更高效，不会导致整个列表重新渲染
						console.log('追加新订单数据:', newOrders.length, '条');
						Array.prototype.push.apply(this.orders, newOrders);
					}

					// 更新是否有更多数据的标志
					this.hasMore = res.data.page < res.data.total_page;
					console.log('是否有更多数据:', this.hasMore);
				} else {
					console.error('获取订单列表失败:', res);
					uni.showToast({
						title: res.message || '获取订单列表失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载订单列表出错:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				this.loadingMore = false;
				this.refreshing = false;
			}
		},

		// 切换状态过滤器
		changeStatus(status) {
			console.log('切换订单状态:', status);
			if (this.activeStatus === status) return;
			this.activeStatus = status;
			this.loadOrders();
		},

		// 加载更多
		loadMore() {
			if (this.loading || this.loadingMore || !this.hasMore) {
				console.log('跳过加载更多: loading=', this.loading, 'loadingMore=', this.loadingMore, 'hasMore=', this.hasMore);
				return;
			}
			console.log('加载更多订单 - 执行loadMore方法');
			this.page++;
			this.loadOrders(false);
		},

		// 下拉刷新
		onRefresh() {
			console.log('下拉刷新订单列表');
			this.refreshing = true;
			this.loadOrders();
		},

		// 跳转到订单详情页
		goToDetail(orderId) {
			console.log('跳转到订单详情:', orderId);
			uni.navigateTo({
				url: `/pages/order_detail/order_detail?id=${orderId}`
			});
		},

		// 取消订单
		async cancelOrder(orderId) {
			console.log('取消订单:', orderId);
			try {
				uni.showLoading({
					title: '取消中...'
				});

				const res = await updateOrderStatus(orderId, this.userId, 4); // 4表示已取消

				if (res && res.code === 0) {
					uni.showToast({
						title: '订单已取消',
						icon: 'success'
					});

					// 更新本地订单状态
					const orderIndex = this.orders.findIndex(order => order.id === orderId);
					if (orderIndex !== -1) {
						// 使用Vue.set更新状态，确保响应式更新
						this.$set(this.orders[orderIndex], 'status', 4);
					}
				} else {
					uni.showToast({
						title: res.message || '取消订单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('取消订单出错:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 支付订单
		async payOrder(orderId) {
			console.log('支付订单:', orderId);
			try {
				uni.showLoading({
					title: '支付处理中...'
				});

				// 这里应该调用实际的支付API
				// 模拟支付成功
				const paymentSuccess = true;

				if (paymentSuccess) {
					// 1. 更新订单状态为已支付(状态码1)
					const updateRes = await updateOrderStatus(orderId, this.userId, 1);

					if (updateRes && updateRes.code === 0) {
						// 更新本地订单状态
						const orderIndex = this.orders.findIndex(order => order.id === orderId);
						if (orderIndex !== -1) {
							this.$set(this.orders[orderIndex], 'status', 1);
						}

						// 2. 调用打印订单API
						const printRes = await printOrder(orderId, this.userId);

						if (printRes && printRes.code === 0) {
							uni.showToast({
								title: '已支付，打印中',
								icon: 'success'
							});

							// 更新本地订单状态为打印中
							if (orderIndex !== -1) {
								this.$set(this.orders[orderIndex], 'status', 2); // 2表示打印中
							}
						} else {
							uni.showToast({
								title: printRes.message || '打印失败',
								icon: 'none'
							});
						}
					} else {
						uni.showToast({
							title: updateRes.message || '支付失败',
							icon: 'none'
						});
					}
				} else {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('支付订单出错:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 获取订单状态文本
		getStatusText(status) {
			const statusMap = {
				0: '待支付',
				1: '已支付',
				2: '打印中',
				3: '已完成',
				4: '已取消',
				5: '已退款'
			};
			return statusMap[status] || '未知状态';
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},

		onScrollToLower(e) {
			console.log('触发了 scrolltolower 事件', e);
			this.loadMore();
		},

		onScroll(e) {
			// 添加更详细的滚动信息
			const { scrollTop, scrollHeight } = e.detail;
			const height = this.scrollViewHeight;

			// 保存当前滚动位置
			this.currentScrollTop = scrollTop;

			// 减少日志输出频率，只在滚动位置变化较大时输出
			if (Math.abs(scrollTop - this._lastLoggedScrollTop) > 100 || !this._lastLoggedScrollTop) {
				this._lastLoggedScrollTop = scrollTop;
			}

			// 手动检测是否接近底部
			if (scrollHeight > 0 && height > 0 && scrollHeight - scrollTop - height < 100) {
				console.log('接近底部，手动触发加载更多');
				this.loadMore();
			}
		},

		onReady() {
			// 计算scroll-view的高度
			const windowInfo = uni.getWindowInfo();
			const systemSetting = uni.getSystemSetting();
			// 获取状态栏高度
			const statusBarHeight = systemSetting.statusBarHeight || 0;
			// 假设顶部筛选器高度约为100rpx (转换为px)
			const filterHeight = 100 * (windowInfo.windowWidth / 750);
			// 设置scroll-view高度为屏幕高度减去状态栏和筛选器高度
			this.scrollViewHeight = windowInfo.windowHeight - statusBarHeight - filterHeight;
		}
	}
}
</script>

<style lang="scss">
.order-container {
	display: flex;
	flex-direction: column;
	height: 100vh;
	background-color: #f5f5f5;
}

.status-filter {
	display: flex;
	background-color: #fff;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;

	.filter-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #666;
		position: relative;
		padding: 15rpx 0;

		&.active {
			color: #1aad19;
			font-weight: bold;

			&:after {
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background-color: #1aad19;
				border-radius: 2rpx;
			}
		}
	}
}

.order-list {
	flex: 1;
}

.order-list-inner {
	padding: 20rpx;
}

.loading-box, .empty-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.order-item {
	background-color: #fff;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;

	.store-name {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}

	.order-status {
		font-size: 26rpx;

		&.status-0 {
			color: #ff9800;
		}

		&.status-1, &.status-2 {
			color: #1aad19;
		}

		&.status-3 {
			color: #8e8e8e;
		}

		&.status-4, &.status-5 {
			color: #999;
		}
	}
}

.order-content {
	margin-bottom: 20rpx;

	.file-list {
		.file-item {
			display: flex;
			align-items: center;
			padding: 15rpx 15rpx;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			.file-icon {
				width: 80rpx;
				height: 80rpx;
				margin-right: 20rpx;
			}

			.file-info {
				flex: 1;

				.file-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 8rpx;
					white-space: normal;
					word-break: break-all;
					overflow: visible;
				}

				.file-pages {
					font-size: 24rpx;
					color: #999;
				}
			}
		}

		.file-more {
			font-size: 24rpx;
			color: #999;
			padding: 10rpx 0;
			text-align: center;
		}
	}
}

.order-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1rpx solid #f5f5f5;

	.order-time {
		font-size: 24rpx;
		color: #999;
	}

	.order-price {
		font-size: 26rpx;
		color: #ff6b00;

		.price-value {
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}

.order-actions {
	display: flex;
	justify-content: flex-end;
	margin-top: 30rpx;

	.action-btn {
		padding: 0 30rpx;
		height: 60rpx;
		line-height: 60rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-left: 10rpx;
		margin-right: 10rpx;
		background-color: #fff;
	}

	.pay-btn {
		background-color: #1aad19;
		color: white;
	}

	.cancel-btn {
		border: 1rpx solid #ccc;
		color: #666;
	}
}

.load-more {
	padding: 20rpx 0;
	text-align: center;
}

.scroll-container {
	flex: 1;
}

.debug-info {
	background-color: rgba(0,0,0,0.7);
	color: #fff;
	padding: 10rpx;
	font-size: 24rpx;
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
	display: flex;
	flex-direction: column;
}
</style>
