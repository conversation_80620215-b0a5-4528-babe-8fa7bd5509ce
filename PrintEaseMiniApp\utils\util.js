// 格式化时间
export const formatTime = (date) => {
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const hour = date.getHours()
	const minute = date.getMinutes()
	const second = date.getSeconds()

	return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
	n = n.toString()
	return n[1] ? n : `0${n}`
}

// 文件大小格式化
export const formatFileSize = (size) => {
	if (size < 1024) {
		return size + 'B'
	} else if (size < 1024 * 1024) {
		return (size / 1024).toFixed(2) + 'KB'
	} else if (size < 1024 * 1024 * 1024) {
		return (size / (1024 * 1024)).toFixed(2) + 'MB'
	} else {
		return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
	}
}

export default {
	formatTime,
	formatFileSize
} 