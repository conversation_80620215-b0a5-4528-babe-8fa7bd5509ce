{"projectid": "1108100302_PrintEaseMiniApp", "setting": {"urlCheck": false, "es6": true, "postcss": false, "minified": true, "newFeature": true, "autoAudits": false, "nodeModules": true, "uploadWithSourceMap": true, "uglifyFileName": true, "remoteDebugLogEnable": false, "prefetch": false, "compileWorklet": false, "enhance": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "hotReload": false, "hot": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "createTime": 1740798380871, "accessTime": 1740798380871, "packOptions": {"ignore": [], "include": []}, "qqappid": "1108100302", "projectname": "PrintEaseMiniApp", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}, "appid": "wx2165aa57fd6b86ad", "simulatorPluginLibVersion": {}, "editorSetting": {}}