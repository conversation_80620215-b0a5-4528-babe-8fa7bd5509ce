import json
import os

class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
            
        try:
            with open(self.config_file, "r") as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件 {self.config_file} 格式错误: {str(e)}")
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, "w") as f:
            json.dump(self.config, f, indent=4)
    
    def get_api_url(self):
        """获取API URL"""
        return self.config["api_url"]
    
    def set_api_url(self, url):
        """设置API URL"""
        self.config["api_url"] = url
        self.save_config()
    
    def get_auth_token(self):
        """获取认证token"""
        return self.config.get("auth", {}).get("token")
    
    def get_merchant_id(self):
        """获取商家ID"""
        return self.config.get("auth", {}).get("merchant_id")
    
    def set_auth_token(self, token, username=None, remember_me=False, merchant_id=None):
        """设置认证信息"""
        if "auth" not in self.config:
            self.config["auth"] = {}
        
        self.config["auth"]["token"] = token
        self.config["auth"]["username"] = username
        self.config["auth"]["remember_me"] = remember_me
        
        if merchant_id is not None:
            self.config["auth"]["merchant_id"] = merchant_id
            
        self.save_config()
    
    def clear_auth_token(self):
        """清除认证token"""
        if "auth" in self.config:
            self.config["auth"]["token"] = None
            self.save_config()
    
    def get_websocket_config(self):
        """获取WebSocket配置"""
        return self.config["websocket"]
    
    def set_websocket_config(self, enabled=None, heartbeat_interval=None, 
                            reconnect_delay=None, max_reconnect_delay=None):
        """设置WebSocket配置"""
        if enabled is not None:
            self.config["websocket"]["enabled"] = enabled
        if heartbeat_interval is not None:
            self.config["websocket"]["heartbeat_interval"] = heartbeat_interval
        if reconnect_delay is not None:
            self.config["websocket"]["reconnect_delay"] = reconnect_delay
        if max_reconnect_delay is not None:
            self.config["websocket"]["max_reconnect_delay"] = max_reconnect_delay
        self.save_config()
    
    def get_uploads_dir(self):
        """获取上传文件保存目录"""
        return self.config["uploads_dir"]
    
    def set_uploads_dir(self, directory):
        """设置上传文件保存目录"""
        self.config["uploads_dir"] = directory
        self.save_config()

    def get_database_path(self):
        """获取数据库文件路径"""
        data_dir = self.config["data_dir"]
        db_name = self.config["database"]["name"]
        return os.path.join(data_dir, db_name)
    
    def set_database_path(self, path):
        """设置数据库文件路径"""
        dir_name = os.path.dirname(path)
        file_name = os.path.basename(path)
        self.config["data_dir"] = dir_name
        self.config["database"]["name"] = file_name
        self.save_config()
