import json
import time
import threading
import logging
import asyncio
import websockets
import jwt
from PyQt5.QtCore import QObject, pyqtSignal
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)

class WebSocketClient(QObject):
    """WebSocket客户端，负责与后端服务器建立WebSocket连接并处理消息"""
    
    # 定义信号
    connected = pyqtSignal()
    disconnected = pyqtSignal(str)
    message_received = pyqtSignal(dict)
    connection_error = pyqtSignal(str)
    
    def __init__(self, base_url, merchant_id, token):
        """初始化WebSocket客户端
        
        Args:
            base_url (str): WebSocket服务器基础URL
            merchant_id (int): 商家ID
            token (str): 认证令牌
        """
        super().__init__()
        
        # 初始化配置管理器
        self.config_manager = ConfigManager()
        
        # 从token中解析商店信息
        try:
            # JWT token通常由三部分组成，用.分隔，第二部分是payload
            payload = jwt.decode(token, options={"verify_signature": False})
            self.store_name = payload.get('store_name', '')
            logger.info(f"从token中获取到商店名称: {self.store_name}")
        except Exception as e:
            logger.error(f"解析token失败: {e}")
            self.store_name = f"Store_{merchant_id}"
        
        # 配置参数
        self.base_url = base_url.replace('/api', '').replace('http://', 'ws://').replace('https://', 'wss://')
        
        # 确保URL格式正确
        if not self.base_url.endswith('/'):
            self.base_url += '/'
        
        # 添加详细日志
        logger.info(f"原始base_url: {base_url}")
        logger.info(f"转换后base_url: {self.base_url}")
        
        # 构建WebSocket URL
        self.ws_url = f"{self.base_url}ws/print-agent?merchant_id={merchant_id}"
        
        # 设置认证头
        self.headers = {
            "Authorization": f"Bearer {token}"
        }
        
        # 打印完整URL和头信息用于调试
        logger.info(f"WebSocket连接URL: {self.ws_url}")
        logger.info(f"WebSocket认证头: Authorization: Bearer {token[:20]}...")
        
        self.merchant_id = merchant_id
        self.token = token
        self.websocket = None
        self.is_connected = False
        self.should_reconnect = True
        
        # 从配置管理器获取WebSocket配置
        ws_config = self.config_manager.get_websocket_config()
        self.reconnect_delay = ws_config["reconnect_delay"]  # 重连延迟（秒）
        self.max_reconnect_delay = ws_config["max_reconnect_delay"]  # 最大重连延迟（秒）
        self.heartbeat_interval = ws_config["heartbeat_interval"]  # 心跳间隔（秒）
        
        # 异步事件循环和线程
        self.loop = None
        self.thread = None
        
    def start(self):
        """启动WebSocket客户端"""
        if self.thread is not None:
            logger.warning("WebSocket客户端已经在运行")
            return
            
        self.should_reconnect = True
        self.thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self.thread.start()
        logger.info("WebSocket客户端线程已启动")
        
    def stop(self):
        """停止WebSocket客户端"""
        self.should_reconnect = False
        if self.loop and self.is_connected:
            asyncio.run_coroutine_threadsafe(self._close_connection(), self.loop)
        logger.info("WebSocket客户端已停止")
        
    def _run_event_loop(self):
        """在单独的线程中运行事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.run_until_complete(self._connection_handler())
        except Exception as e:
            logger.error(f"WebSocket事件循环异常: {e}")
        finally:
            self.loop.close()
            self.loop = None
            self.thread = None
            
    async def _connection_handler(self):
        """处理WebSocket连接和重连"""
        while self.should_reconnect:
            try:
                # 使用已构建好的URL和认证头
                async with websockets.connect(self.ws_url, extra_headers=self.headers) as websocket:
                    self.websocket = websocket
                    self.is_connected = True
                    self.reconnect_delay = 1  # 重置重连延迟
                    logger.info("WebSocket连接已建立")
                    self.connected.emit()

                    # 发送认证消息
                    await self._send_auth_message()
                    
                    # 启动心跳任务
                    heartbeat_task = asyncio.create_task(self._heartbeat_handler())
                    
                    # 接收消息
                    try:
                        await self._message_handler()
                    finally:
                        heartbeat_task.cancel()
                        
            except (websockets.exceptions.ConnectionClosed, 
                    websockets.exceptions.InvalidStatusCode,
                    ConnectionRefusedError) as e:
                self.is_connected = False
                error_msg = f"WebSocket连接错误: {str(e)}"
                logger.error(error_msg)
                self.connection_error.emit(error_msg)
                
                if self.should_reconnect:
                    logger.info(f"尝试在 {self.reconnect_delay} 秒后重连...")
                    await asyncio.sleep(self.reconnect_delay)
                    # 指数退避重连策略
                    self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
            
            except Exception as e:
                self.is_connected = False
                error_msg = f"WebSocket未知错误: {str(e)}"
                logger.error(error_msg)
                self.connection_error.emit(error_msg)
                
                if self.should_reconnect:
                    logger.info(f"尝试在 {self.reconnect_delay} 秒后重连...")
                    await asyncio.sleep(self.reconnect_delay)
                    self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
    
    async def _close_connection(self):
        """关闭WebSocket连接"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            self.disconnected.emit("连接已关闭")
            logger.info("WebSocket连接已关闭")
    
    async def _send_auth_message(self):
        """发送认证消息"""
        auth_message = {
            "type": "auth",
            "data": {
                "merchant_id": self.merchant_id,
                "agent_name": f"{self.store_name}_Agent",
                "agent_version": "1.0.0",  # 代理程序版本
                "token": self.token
            }
        }
        # 使用ensure_ascii=False确保中文正常显示
        await self.websocket.send(json.dumps(auth_message, ensure_ascii=False))
        logger.info("已发送认证消息")
    
    async def _heartbeat_handler(self):
        """定期发送心跳消息"""
        while self.is_connected:
            try:
                heartbeat_message = {
                    "type": "heartbeat",
                    "timestamp": int(time.time())
                }
                await self.websocket.send(json.dumps(heartbeat_message, ensure_ascii=False))
                await asyncio.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"发送心跳消息失败: {e}")
                break
    
    async def _message_handler(self):
        """处理接收到的消息"""
        while self.is_connected:
            try:
                message = await self.websocket.recv()
                logger.debug(f"收到消息: {message}")
                
                try:
                    message_data = json.loads(message)
                    # 发送信号，通知UI线程处理消息
                    self.message_received.emit(message_data)
                except json.JSONDecodeError:
                    logger.error(f"无效的JSON消息: {message}")
                    
            except websockets.exceptions.ConnectionClosed:
                logger.info("WebSocket连接已关闭")
                self.is_connected = False
                self.disconnected.emit("连接已关闭")
                break
                
            except Exception as e:
                logger.error(f"处理消息时出错: {e}")
                self.is_connected = False
                self.disconnected.emit(f"处理消息错误: {str(e)}")
                break
    
    def send_message(self, message):
        """发送消息到服务器
        
        Args:
            message (dict): 要发送的消息
        """
        if not self.is_connected or not self.websocket:
            logger.error("无法发送消息: WebSocket未连接")
            return False
            
        if self.loop:
            asyncio.run_coroutine_threadsafe(
                self._send_message_async(message), 
                self.loop
            )
            return True
        return False
    
    async def _send_message_async(self, message):
        """异步发送消息
        
        Args:
            message (dict): 要发送的消息
        """
        try:
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            logger.debug(f"已发送消息: {message}")
            return True
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    def send_status_update(self, task_id, status, message=""):
        """发送任务状态更新
        
        Args:
            task_id (str): 任务ID
            status (str): 任务状态
            message (str, optional): 状态描述
        """
        status_message = {
            "type": "status_update",
            "task_id": task_id,
            "status": status,
            "message": message,
            "timestamp": int(time.time())
        }
        return self.send_message(status_message)
    
    def send_error(self, task_id, error_code, message):
        """发送错误消息
        
        Args:
            task_id (str): 任务ID
            error_code (str): 错误代码
            message (str): 错误描述
        """
        error_message = {
            "type": "error",
            "task_id": task_id,
            "error_code": error_code,
            "message": message,
            "timestamp": int(time.time())
        }
        return self.send_message(error_message) 