package handlers

import (
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"PrintEaseBackend/internal/services"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// UploadResponse 上传响应结构
type UploadResponse struct {
	FileID     uint64 `json:"file_id"`
	CartItemID uint64 `json:"cart_item_id"`
	Filename   string `json:"filename"`
	FileSize   int64  `json:"file_size"`
	FileType   string `json:"file_type"`
	PageCount  int    `json:"page_count"`
}

// UploadHandler 处理文件上传
func UploadHandler(c *gin.Context) {
	// 检查请求方法
	if c.Request.Method != "POST" {
		c.JSON(http.StatusMethodNotAllowed, gin.H{
			"code": -1,
			"msg":  "只支持POST方法",
		})
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("获取上传文件失败: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "获取上传文件失败",
		})
		return
	}
	defer file.Close()

	// 确定文件名（自定义或默认）
	filename := header.Filename
	customFilename := c.PostForm("filename")
	if customFilename != "" {
		filename = customFilename
	}

	// 验证文件类型
	if err := models.ValidateFileType(filename); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  err.Error(),
		})
		return
	}

	// 验证文件大小
	if header.Size > int64(config.GlobalConfig.Upload.MaxFileSize)<<20 { // 转换为字节
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  fmt.Sprintf("文件大小超过限制(%dMB)", config.GlobalConfig.Upload.MaxFileSize),
		})
		return
	}

	// 创建上传目录
	today := time.Now().Format("2006-01-02")
	uploadDir := filepath.Join(config.GlobalConfig.Upload.UploadDir, today)
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		log.Printf("创建上传目录失败: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "创建上传目录失败",
		})
		return
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		ext = ".unknown"
	}

	// 保存文件
	filePath := filepath.Join(uploadDir, filename)

	// 创建文件记录
	fileModel := &models.File{
		UserID:    1, // TODO: 从认证中获取用户ID
		Filename:  filename,
		FilePath:  filePath,
		FileSize:  header.Size,
		FileType:  ext[1:], // 去掉扩展名前面的点
		Status:    models.FileStatusNormal,
		PageCount: 1, // 默认先设置为1页
	}

	// 保存文件到磁盘
	if err := c.SaveUploadedFile(header, filePath); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "保存文件失败: " + err.Error(),
		})
		return
	}

	// 保存文件记录到数据库
	if err := database.DB.Create(fileModel).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "保存文件记录失败: " + err.Error(),
		})
		return
	}

	// 创建购物车项目
	cartItem, err := models.CreateCartItem(1, fileModel.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "创建购物车项目失败: " + err.Error(),
		})
		return
	}

	// 使用goroutine异步计算页数
	go func(filePath string, fileID uint64) {
		// 计算文件页数
		fileService := &services.FileService{}
		pageCount, err := fileService.CalculatePageCount(filePath)
		if err != nil {
			log.Printf("计算文件页数出错: %v, 使用默认页数1\n", err)
			return
		}

		// 更新数据库中的页数
		if err := database.DB.Model(&models.File{}).Where("id = ?", fileID).
			Update("page_count", pageCount).Error; err != nil {
			log.Printf("更新文件页数失败: %v\n", err)
		} else {
			log.Printf("成功更新文件 %d 的页数为: %d\n", fileID, pageCount)
		}
	}(filePath, fileModel.ID)

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "上传成功",
		"data": UploadResponse{
			FileID:     fileModel.ID,
			CartItemID: cartItem.ID,
			Filename:   fileModel.Filename,
			FileSize:   fileModel.FileSize,
			FileType:   fileModel.FileType,
			PageCount:  fileModel.PageCount, // 返回初始页数1
		},
	})
}
