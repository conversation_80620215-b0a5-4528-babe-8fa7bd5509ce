package services

import (
	"bufio"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// FileService 文件服务
type FileService struct{}

// CalculatePageCount 根据文件类型计算页数
func (s *FileService) CalculatePageCount(filePath string) (int, error) {
	ext := strings.ToLower(filepath.Ext(filePath))

	// 根据文件扩展名选择不同的页数计算方法
	switch ext {
	case ".pdf":
		return s.calculatePDFPageCount(filePath)
	case ".txt":
		return s.calculateTxtPageCount(filePath)
	case ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx":
		return s.calculateOfficePageCount(filePath)
	case ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tif", ".tiff":
		// 图片文件默认为1页
		return 1, nil
	default:
		// 默认返回1页
		return 1, nil
	}
}

// calculatePDFPageCount 计算PDF文件页数
func (s *FileService) calculatePDFPageCount(filePath string) (int, error) {
	// 转换文件路径格式，将反斜杠替换为正斜杠
	gsFilePath := strings.ReplaceAll(filePath, "\\", "/")

	// 使用Ghostscript计算PDF页数，添加 -dNOSAFER 参数
	cmd := exec.Command("gswin64c", "-q", "-dNODISPLAY", "-dNOSAFER", "-c",
		fmt.Sprintf("(%s) (r) file runpdfbegin pdfpagecount = quit", gsFilePath))

	// 添加超时控制
	timeout := time.Duration(30 * time.Second)
	timer := time.AfterFunc(timeout, func() {
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
	})
	defer timer.Stop()

	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("计算PDF页数出错: %v, 默认返回1页\n", err)
		return 1, err
	}

	// 解析输出获取页数
	pageCount, err := strconv.Atoi(strings.TrimSpace(string(output)))
	if err != nil {
		fmt.Printf("解析PDF页数出错: %v, 默认返回1页\n", err)
		return 1, err
	}

	if pageCount < 1 {
		pageCount = 1
	}

	return pageCount, nil
}

// calculateTxtPageCount 计算TXT文件页数
func (s *FileService) calculateTxtPageCount(filePath string) (int, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Printf("打开TXT文件出错: %v, 默认返回1页\n", err)
		return 1, err
	}
	defer file.Close()

	// 统计行数
	scanner := bufio.NewScanner(file)
	lineCount := 0

	// 设置最大扫描行数，避免过大文件消耗过多资源
	maxLines := 10000

	for scanner.Scan() && lineCount < maxLines {
		lineCount++
	}

	// 如果文件超过最大行数，按最大行数计算
	if lineCount >= maxLines {
		fmt.Printf("TXT文件超过%d行，按最大行数计算\n", maxLines)
	}

	// 检查扫描时是否发生错误
	if err := scanner.Err(); err != nil {
		fmt.Printf("读取TXT文件出错: %v, 使用已读取的行数计算\n", err)
	}

	// 按照标准A4纸张60行/页估算
	linesPerPage := 60
	pageCount := (lineCount + linesPerPage - 1) / linesPerPage // 向上取整

	if pageCount < 1 {
		pageCount = 1 // 至少1页
	}

	return pageCount, nil
}

// calculateOfficePageCount 计算Office文档页数
func (s *FileService) calculateOfficePageCount(filePath string) (int, error) {
	// 创建临时目录用于转换文件
	tempDir := os.TempDir()
	fileName := filepath.Base(filePath)
	fileNameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))
	pdfOutputPath := filepath.Join(tempDir, fileNameWithoutExt+".pdf")

	// 使用LibreOffice转换为PDF
	cmd := exec.Command("soffice", "--headless", "--convert-to", "pdf",
		"--outdir", tempDir, filePath)

	// 添加超时控制
	timeout := time.Duration(60 * time.Second)
	timer := time.AfterFunc(timeout, func() {
		if cmd.Process != nil {
			cmd.Process.Kill()
		}
	})
	defer timer.Stop()

	// 执行转换命令
	_, err := cmd.Output()
	if err != nil {
		fmt.Printf("转换Office文档出错: %v, 默认返回1页\n", err)
		return 1, err
	}

	// 计算转换后PDF的页数
	pageCount, err := s.calculatePDFPageCount(pdfOutputPath)

	// 清理临时文件
	defer os.Remove(pdfOutputPath)

	if err != nil {
		fmt.Printf("计算Office文档页数出错: %v, 默认返回1页\n", err)
		return 1, err
	}

	return pageCount, nil
}
