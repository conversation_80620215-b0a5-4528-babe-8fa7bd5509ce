from typing import Optional, Dict, Any, List
from datetime import datetime
from .base import BaseManager, logger

class PrinterManager(BaseManager):
    """打印机数据库管理类"""

    def _create_tables(self):
        """创建打印机相关表"""
        if not self.connection:
            raise Exception("数据库未连接")

        # 创建打印机表
        self.connection.execute("""
        CREATE TABLE IF NOT EXISTS printers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            -- 打印机基本信息
            name VARCHAR(100) NOT NULL,                -- 打印机名称
            printer_id VARCHAR(50) NOT NULL,           -- 打印机唯一标识（与后台对应）
            system_name VARCHAR(100) NOT NULL,         -- 系统中的打印机名称

            -- 打印机状态
            status INTEGER NOT NULL DEFAULT 0,         -- 打印机状态：0-离线 1-在线 2-打印中 3-错误
            error_message TEXT,                        -- 错误信息
            paper_status INTEGER DEFAULT 1,            -- 纸张状态：0-缺纸 1-正常

            -- 打印机配置
            config TEXT,                              -- 打印机配置（JSON格式）
                                                      -- 配置JSON格式示例：
                                                      -- {
                                                      --     "default_settings": {
                                                      --         "color_mode": "black",     -- 默认打印模式：black/color
                                                      --         "duplex": true,           -- 默认双面打印
                                                      --         "copies": 1,              -- 默认打印份数
                                                      --         "paper_size": "A4",       -- 默认纸张大小
                                                      --         "orientation": "portrait", -- 默认方向：portrait/landscape
                                                      --         "quality": "normal"       -- 默认打印质量：draft/normal/high
                                                      --     },
                                                      --     "supported_paper": [          -- 支持的纸张类型列表
                                                      --         {
                                                      --             "name": "A4",         -- 纸张名称
                                                      --             "width": 210,         -- 宽度（毫米）
                                                      --             "height": 297,        -- 高度（毫米）
                                                      --             "default": true       -- 是否默认
                                                      --         }
                                                      --     ],
                                                      --     "supported_features": {       -- 打印机支持的功能
                                                      --         "color": true,           -- 是否支持彩色打印
                                                      --         "duplex": true,          -- 是否支持双面打印
                                                      --         "paper_sizes": [         -- 支持的纸张尺寸列表
                                                      --             "A3", "A4", "B4", "B5"
                                                      --         ],
                                                      --         "quality_options": [      -- 支持的打印质量选项
                                                      --             "draft",             -- 草稿质量
                                                      --             "normal",            -- 正常质量
                                                      --             "high"               -- 高质量
                                                      --         ]
                                                      --     },
                                                      --     "printer_info": {            -- 打印机硬件信息
                                                      --         "manufacturer": "HP",     -- 制造商
                                                      --         "model": "LaserJet Pro",  -- 型号
                                                      --         "driver_name": "HP LaserJet Pro Driver",  -- 驱动名称
                                                      --         "driver_version": "1.0.0" -- 驱动版本
                                                      --     }
                                                      -- }

            -- 同步相关
            backend_id INTEGER,                       -- 后台系统中的打印机ID
            last_sync TIMESTAMP,                      -- 最后同步时间
            sync_status INTEGER DEFAULT 0,            -- 同步状态：0-未同步 1-已同步 2-同步失败

            -- 系统信息
            os_type VARCHAR(20) NOT NULL,             -- 操作系统类型：windows/linux/macos
            driver_name TEXT,                         -- 打印机驱动名称
            port_name TEXT,                           -- 打印机端口名称

            -- 时间信息
            created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),
            updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),

            -- 唯一索引
            UNIQUE(printer_id),
            UNIQUE(system_name, os_type)
        )
        """)

        # 检查并删除已存在的触发器
        self.connection.execute("""
        DROP TRIGGER IF EXISTS update_printers_timestamp;
        """)

        # 创建打印机状态更新触发器
        self.connection.execute("""
        CREATE TRIGGER update_printers_timestamp
        AFTER UPDATE ON printers
        FOR EACH ROW
        BEGIN
            UPDATE printers SET updated_at = datetime('now', 'localtime')
            WHERE id = old.id;
        END;
        """)

        self.connection.commit()

    def add_printer(self, name: str, printer_id: str, system_name: str, os_type: str,
                   driver_name: str = None, port_name: str = None, config: str = None) -> int:
        """添加新的打印机记录"""
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO printers (
                    name, printer_id, system_name, os_type,
                    driver_name, port_name, config,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
            """, (name, printer_id, system_name, os_type,
                 driver_name, port_name, config))
            self.connection.commit()
            return cursor.lastrowid
        finally:
            self.close()

    def update_printer_status(self, printer_id: str, status: int,
                            error_message: str = None, paper_status: int = None):
        """更新打印机状态"""
        try:
            self.connect()
            update_fields = ["status = ?"]
            params = [status]

            if error_message is not None:
                update_fields.append("error_message = ?")
                params.append(error_message)

            if paper_status is not None:
                update_fields.append("paper_status = ?")
                params.append(paper_status)

            params.append(printer_id)

            # 添加更新时间字段
            update_fields.append("updated_at = datetime('now', 'localtime')")

            sql = f"""
                UPDATE printers
                SET {', '.join(update_fields)}
                WHERE printer_id = ?
            """
            self.connection.execute(sql, params)
            self.connection.commit()
        finally:
            self.close()

    def update_printer_sync_status(self, printer_id: str, backend_id: int,
                                 sync_status: int = 1):
        """更新打印机同步状态"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers
                SET backend_id = ?, sync_status = ?, last_sync = datetime('now', 'localtime')
                WHERE printer_id = ?
            """, (backend_id, sync_status, printer_id))
            self.connection.commit()
        finally:
            self.close()

    def update_printer_config(self, printer_id: str, config: str):
        """更新打印机配置"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers
                SET config = ?, updated_at = datetime('now', 'localtime')
                WHERE printer_id = ?
            """, (config, printer_id))
            self.connection.commit()
        finally:
            self.close()

    def get_printer_by_id(self, printer_id: str) -> Optional[Dict[str, Any]]:
        """根据打印机ID获取打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers
                WHERE printer_id = ?
            """, (printer_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_printer_by_system_name(self, system_name: str,
                                 os_type: str) -> Optional[Dict[str, Any]]:
        """根据系统打印机名称获取打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers
                WHERE system_name = ? AND os_type = ?
            """, (system_name, os_type))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_all_printers(self) -> List[Dict[str, Any]]:
        """获取所有打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def get_online_printers(self) -> List[Dict[str, Any]]:
        """获取所有在线的打印机"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers
                WHERE status = 1
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def delete_printer(self, printer_id: str):
        """删除打印机记录"""
        try:
            self.connect()
            self.connection.execute("""
                DELETE FROM printers
                WHERE printer_id = ?
            """, (printer_id,))
            self.connection.commit()
        finally:
            self.close()

    def update_printer_name(self, printer_id: str, name: str):
        """更新打印机名称"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers
                SET name = ?, updated_at = datetime('now', 'localtime')
                WHERE printer_id = ?
            """, (name, printer_id))
            self.connection.commit()
        finally:
            self.close()