<template>
	<view class="min-h-screen bg-gray-50">
		<!-- 固定部分：Tab Navigation -->
		<view class="fixed-header">
			<view class="flex bg-white border-b">
				<view class="flex-1 px-4 py-3 text-center relative">
					<text class="text-emerald-600">选择门店</text>
					<view class="absolute bottom-0 left-0 right-0 h-0-5 bg-emerald-600"></view>
				</view>
				<view class="flex-1 px-4 py-3 text-center text-gray-600">常去/收藏</view>
			</view>
		</view>

		<!-- 地图区域 -->
		<view class="map-container" :class="{ 'map-collapsed': !isMapExpanded }">
			<!-- 地图部分 -->
			<view class="map-wrapper">
				<map
					id="map"
					class="map"
					:latitude="latitude"
					:longitude="longitude"
					:markers="markers"
					:show-location="true"
					@markertap="handleMarkerTap"
				></map>
			</view>

			<!-- 地图控制按钮 -->
			<view class="map-control" @tap="toggleMap">
				<text>{{ isMapExpanded ? '收起地图' : '展开地图' }}</text>
				<image 
					:src="'/static/icons/chevron-' + (isMapExpanded ? 'up' : 'down') + '.png'" 
					class="w-4 h-4 ml-1" 
				/>
			</view>
		</view>

		<!-- 商家列表 -->
		<scroll-view 
			class="store-list-scroll"
			:class="{ 'list-expanded': !isMapExpanded }"
			scroll-y
			@scrolltolower="loadMore"
		>
			<view class="store-list">
				<view 
					v-for="(store, index) in stores" 
					:key="index"
					:id="'store-' + index"
					:class="[
						'bg-white rounded-lg p-4 relative',
						index > 0 ? 'space-y-3-item' : '',
						store.selected ? 'selected-store' : ''
					]"
					@tap="handleStoreClick(index)"
					:ref="'store-' + index"
				>
					<!-- Selected Corner Mark -->
					<view v-if="store.selected" class="absolute -top-px -right-px w-12 h-12 overflow-hidden">
						<view class="absolute top-0 right-0 w-12 h-12">
							<view class="corner-triangle"></view>
							<image src="/static/icons/check-white.png" class="absolute top-1 right-1 w-4 h-4" />
						</view>
					</view>

					<view class="flex">
						<!-- Left Section -->
						<view class="flex-1 pr-4 border-r border-gray-200">
							<view class="flex items-center mb-2">
								<image :src="store.isFavorite ? '/static/icons/star-filled.png' : '/static/icons/star.png'" class="w-5 h-5" />
								<text class="ml-1 text-lg">{{store.name}}</text>
							</view>

							<view class="text-gray-600">
								<view class="flex items-start">
									<image src="/static/icons/map-pin.png" class="w-4 h-4 mr-2 mt-1 flex-shrink-0" />
									<text class="text-sm">{{store.address}}</text>
								</view>
								<view class="space-y-2-item flex items-center">
									<image src="/static/icons/clock.png" class="w-4 h-4 mr-2" />
									<text class="text-sm">{{store.businessHours}}</text>
								</view>
							</view>

							<view class="mt-3">
								<text class="px-2 py-0-5 border border-emerald-600 text-emerald-600 rounded text-sm">
									{{store.status === 'open' ? '营业中' : '休息中'}}
								</text>
							</view>
						</view>

						<!-- Right Section -->
						<view class="pl-4 flex flex-col items-end justify-center space-y-6">
							<view class="flex flex-col items-end">
								<text class="text-emerald-600 text-sm">距离{{store.distance}}km</text>
							</view>
							
							<view class="flex">
								<view class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
									<image src="/static/icons/phone.png" class="w-5 h-5 text-gray-600" />
								</view>
								<view class="space-x-2-item flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
									<image src="/static/icons/navigation.png" class="w-5 h-5 text-gray-600" />
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 固定在底部的打印按钮 -->
		<view class="fixed-print-btn">
			<button 
				class="print-btn" 
				:class="{ 'print-btn-disabled': currentStoreIndex === -1 }"
				:disabled="currentStoreIndex === -1"
				@tap="selectStore(stores[currentStoreIndex])"
			>
				{{ currentStoreIndex === -1 ? '请选择打印店' : '去打印' }}
			</button>
		</view>
	</view>
</template>

<script>
import { getStoreList } from '@/api/index'

export default {
	data() {
		return {
			longitude: 104.081969, // 默认经度：四川大学
			latitude: 30.634214,   // 默认纬度：四川大学
			scale: 14,             // 地图缩放级别
			stores: [],            // 店铺列表
			markers: [],           // 地图标记点
			userLocation: null,     // 用户位置
			mapContext: null,       // 添加地图上下文
			currentStoreIndex: -1,  // 添加当前选中的商家索引
			scrollTop: 0,  // 添加scrollTop数据
			isMapExpanded: true, // 控制地图展开/收起状态
			action: '', // 页面动作参数：select表示选择店铺后会返回
		}
	},
	onLoad(options) {
		console.log('页面参数:', options);
		if (options.action) {
			this.action = options.action;
		}
		this.initPage()
	},
	onReady() {
		// 获取地图上下文
		this.mapContext = uni.createMapContext('map', this)
	},
	methods: {
		// 页面初始化
		async initPage() {
			try {
				// 获取用户位置
				await this.getUserLocation()
				// 获取店铺列表
				await this.getStores()
				// 更新地图标记点
				this.updateMarkers()
			} catch (err) {
				console.error('初始化页面失败:', err)
			}
		},
		
		// 获取用户位置
		getUserLocation() {
			return new Promise((resolve, reject) => {
				uni.getLocation({
					type: 'gcj02',
					success: (res) => {
						this.longitude = res.longitude
						this.latitude = res.latitude
						this.userLocation = {
							longitude: res.longitude,
							latitude: res.latitude
						}
						this.scale = 14; // 设置初始缩放级别
						resolve(res)
					},
					fail: (err) => {
						uni.showToast({
							title: '获取位置失败',
							icon: 'none'
						})
						reject(err)
					}
				})
			})
		},
		
		// 获取店铺列表
		async getStores() {
			try {
				const res = await getStoreList({
					longitude: this.longitude,
					latitude: this.latitude
				})
				
				console.log('API响应原始数据:', res)
				
				if (!res || !res.data) {
					throw new Error('获取店铺列表失败: 无数据')
				}
				
				// 直接使用返回的数据数组
				const storeList = res.data || []  // 修改这里，直接使用 data
				console.log('解析后的店铺列表:', storeList)
				
				// 处理每个店铺数据
				this.stores = storeList.map(store => {
					console.log('处理店铺数据:', store)
					
					// 确保店铺数据完整
					if (!store || !store.id || !store.name || !store.address || 
						typeof store.latitude === 'undefined' || 
						typeof store.longitude === 'undefined') {
						console.error('店铺数据不完整:', store)
						return null
					}
					
					return {
						id: store.id,
						name: store.name,
						address: store.address,
						latitude: Number(store.latitude),
						longitude: Number(store.longitude),
						status: store.status,
						distance: store.distance || '0.0',
						phone: store.phone,
						businessHours: store.businessHours,
						announcement: store.announcement
					}
				}).filter(store => store !== null)
				
				console.log('最终处理后的店铺数据:', this.stores)
				
				if (this.stores.length === 0) {
					uni.showToast({
						title: '附近暂无打印店',
						icon: 'none',
						duration: 2000
					})
					return
				}
				
				this.updateMarkers()
				
			} catch (err) {
				console.error('获取店铺列表失败:', err)
				uni.showToast({
					title: err.message || '获取店铺列表失败',
					icon: 'none',
					duration: 2000
				})
			}
		},
		
		// 更新地图标记点
		updateMarkers() {
			if (!this.stores || this.stores.length === 0) {
				this.markers = []
				return
			}
			
			this.markers = this.stores.map((store, index) => ({
				id: index,
				latitude: store.latitude,
				longitude: store.longitude,
				title: store.name,
				iconPath: index === this.currentStoreIndex ? '/static/icons/marker-active.png' : '/static/icons/marker-normal.png',
				width: index === this.currentStoreIndex ? 40 : 32,
				height: index === this.currentStoreIndex ? 40 : 32,
				zIndex: index === this.currentStoreIndex ? 100 : 10
			}))
			
			console.log('地图标记点数据:', this.markers)
		},
		
		// 计算两点之间的距离（km）
		calculateDistance(lat1, lon1, lat2, lon2) {
			const R = 6371 // 地球半径（km）
			const dLat = this.deg2rad(lat2 - lat1)
			const dLon = this.deg2rad(lon2 - lon1)
			const a = 
				Math.sin(dLat/2) * Math.sin(dLat/2) +
				Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) * 
				Math.sin(dLon/2) * Math.sin(dLon/2)
			const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
			return R * c
		},
		
		// 角度转弧度
		deg2rad(deg) {
			return deg * (Math.PI/180)
		},
		
		// 处理商家点击
		handleStoreClick(index) {
			const store = this.stores[index]
			this.currentStoreIndex = index

			// 更新地图视图
			if (this.mapContext) {
				this.mapContext.moveToLocation({
					longitude: store.longitude,
					latitude: store.latitude,
					success: () => {
						// 更新所有标记点的样式
						this.markers = this.stores.map((s, i) => ({
							id: i,
							latitude: s.latitude,
							longitude: s.longitude,
							title: s.name,
							iconPath: i === index ? '/static/icons/marker-active.png' : '/static/icons/marker-normal.png',
							width: i === index ? 40 : 32,
							height: i === index ? 40 : 32,
							zIndex: i === index ? 100 : 10
						}))

						// 取消其他商家的选中状态
						this.stores.forEach((s, i) => {
							s.selected = i === index
						})

						// 滚动到顶部
						this.scrollTop = 0
					}
				})
			}
		},

		// 修改标记点点击处理方法
		handleMarkerTap(e) {
			const index = e.detail.markerId
			this.handleStoreClick(index) // 复用商家点击处理方法
		},

		// 选择打印店
		selectStore(store) {
			console.log('选择打印店:', store);
			
			// 将选中的打印店保存到Vuex和本地存储
			this.$store.commit('setSelectedStore', store);
			
			if (this.action === 'select') {
				// 如果是从购物车页面过来选择店铺，则返回选中的店铺
				const eventChannel = this.getOpenerEventChannel();
				eventChannel.emit('selectStore', store);
				// 返回上一页
				uni.navigateBack();
			} else {
				// 普通模式，跳转到打印页面
				uni.switchTab({
					url: '/pages/index/index'
				});
			}
		},

		// 切换地图展开/收起状态
		toggleMap() {
			this.isMapExpanded = !this.isMapExpanded;
		},

		// 添加缩放控制方法
		zoomIn() {
			if (this.scale < 20) {
				this.scale++;
			}
		},
		
		zoomOut() {
			if (this.scale > 3) {
				this.scale--;
			}
		}
	}
}
</script>

<style lang="scss">
.min-h-screen { min-height: 100vh; }
.bg-gray-50 { background-color: #F9FAFB; }
.bg-white { background-color: #FFFFFF; }
.bg-emerald-600 { background-color: #059669; }
.bg-gray-100 { background-color: #F3F4F6; }

.text-emerald-600 { color: #059669; }
.text-gray-600 { color: #4B5563; }
.text-white { color: #FFFFFF; }
.text-yellow-400 { color: #FBBF24; }

.border-emerald-600 { border-color: #059669; }
.border-gray-200 { border-color: #E5E7EB; }
.border-t-emerald-600 { border-top-color: #059669; }
.border-l-transparent { border-left-color: transparent; }

.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }
.rounded-md { border-radius: 0.375rem; }
.rounded { border-radius: 0.25rem; }

.w-0 { width: 0; }
.w-3 { width: 0.75rem; }
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }

.h-0 { height: 0; }
.h-3 { height: 0.75rem; }
.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-0-5 { height: 0.125rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }

.py-0-5 { padding-top: 0.125rem; padding-bottom: 0.125rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-1-5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

.m-1 { margin: 0.25rem; }
.ml-1 { margin-left: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-auto { margin-top: auto; }

.-top-px { top: -1px; }
.-right-px { right: -1px; }
.-bottom-1 { bottom: -0.25rem; }

.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }

.flex { display: flex; }
.inline-block { display: inline-block; }
.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.flex-shrink-0 { flex-shrink: 0; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.relative { position: relative; }
.absolute { position: absolute; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.left-half { left: 50%; }
.top-half { top: 50%; }
.top-0 { top: 0; }
.top-1 { top: 0.25rem; }
.right-1 { right: 0.25rem; }

.translate-x-half { transform: translateX(-50%); }
.translate-y-half { transform: translateY(-50%); }
.transform-center { transform: translateX(-50%) translateY(-50%); }
.rotate-45 { transform: rotate(45deg); }

.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-r { border-right-width: 1px; }
.border-t-48 { border-top-width: 48px; }
.border-l-48 { border-left-width: 48px; }

.bg-opacity-90 { opacity: 0.9; }

.space-y-2-item { margin-top: 0.5rem; }
.space-y-3-item { margin-top: 0.75rem; }
.space-y-6 { gap: 1.5rem; }
.space-x-2-item { margin-left: 0.5rem; }

.overflow-hidden { overflow: hidden; }
.truncate { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

.map-container {
	position: fixed;
	top: 100rpx;
	left: 0;
	right: 0;
	display: flex;
	flex-direction: column;
	z-index: 99;
	background: #fff;
	
	.map-wrapper {
		height: 35vh;
		transition: all 0.3s ease;
		
		.map {
			width: 100%;
			height: 100%;
		}
	}
	
	&.map-collapsed {
		.map-wrapper {
			height: 0;
			overflow: hidden;
		}
	}
}

.map-control {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
	background: #fff;
	border-bottom: 1rpx solid #eee;
	font-size: 28rpx;
	color: #666;
	height: 88rpx;  /* 固定控制按钮高度 */
	box-sizing: border-box;
}

.store-list-scroll {
	position: fixed;
	top: calc(100rpx + 35vh + 88rpx); /* header高度 + 地图高度 + 控制按钮高度 */
	left: 0;
	right: 0;
	bottom: 128rpx; /* 为底部按钮留出空间 */
	z-index: 98;
	transition: all 0.3s ease;
	background: #F9FAFB;
	
	&.list-expanded {
		top: calc(100rpx + 88rpx); /* 当地图收起时,列表上移到控制按钮下方 */
	}
}

.store-list {
	padding: 20rpx;
}

.store-item {
	background: #fff;
	padding: 20rpx;
	margin-bottom: 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 选中商家的样式 */
.selected-store {
    border: 2rpx solid #059669 !important;  /* emerald-600 */
}

.corner-triangle {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 80rpx 80rpx 0;
    border-color: transparent #059669 transparent transparent;
}

.fixed-print-btn {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	padding: 20rpx 40rpx;
	background: #fff;
	box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.05);
	z-index: 100;
}

.print-btn {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	background: #059669;
	color: #fff;
	font-size: 32rpx;
	border-radius: 44rpx;
	text-align: center;

	&.print-btn-disabled {
		background: #E5E7EB;  /* 使用灰色背景 */
		color: #9CA3AF;      /* 使用灰色文字 */
		cursor: not-allowed;
	}
}
</style>


