<template>
	<view class="detail-container">
		<view v-if="loading" class="loading-box">
			<uni-load-more status="loading" :contentText="loadingText"></uni-load-more>
		</view>

		<block v-else-if="order">
			<!-- 订单状态 -->
			<view class="status-section">
				<view class="status-text">{{ getStatusText(order.status) }}</view>
				<view class="status-desc">{{ getStatusDesc(order.status) }}</view>
			</view>

			<!-- 商家信息 -->
			<view class="store-section section-card">
				<view class="section-title">商家信息</view>
				<view class="store-info">
					<view class="store-name">{{ order.store.name }}</view>
					<view class="store-address">{{ order.store.address }}</view>
					<view class="store-phone" @tap="callStore(order.store.phone)">
						<text class="phone-label">联系电话：</text>
						<text class="phone-value">{{ order.store.phone }}</text>
						<text class="phone-icon">拨打</text>
					</view>
				</view>
			</view>

			<!-- 订单详情 -->
			<view class="order-section section-card">
				<view class="section-title">订单详情</view>
				<view class="order-info">
					<view class="info-item">
						<text class="info-label">订单编号：</text>
						<text class="info-value">{{ order.id }}</text>
					</view>
					<view class="info-item">
						<text class="info-label">创建时间：</text>
						<text class="info-value">{{ formatTime(order.created_at) }}</text>
					</view>
					<view class="info-item" v-if="order.remark">
						<text class="info-label">订单备注：</text>
						<text class="info-value">{{ order.remark }}</text>
					</view>
				</view>
			</view>

			<!-- 文件列表 -->
			<view class="files-section section-card">
				<view class="section-title">文件列表</view>
				<view class="files-list">
					<view class="file-item" v-for="(item, index) in order.order_items" :key="index">
						<view class="file-left">
							<image class="file-icon" :src="getFileIcon(item.cart_item.file.file_type)" mode="aspectFit"></image>
						</view>
						<view class="file-center">
							<view class="file-name">{{ item.cart_item.file.filename }}</view>
							<view class="file-config" v-if="item.cart_item.config">
								{{ formatConfig(item.cart_item.config) }}
							</view>
							<view class="file-info">
								<text class="file-pages">{{ item.cart_item.file.page_count }}页</text>
								<text class="file-size">{{ formatFileSize(item.cart_item.file.file_size) }}</text>
							</view>
						</view>
						<view class="file-right">
							<view class="file-price">￥{{ item.price.toFixed(2) }}</view>
							<button class="file-preview" @tap="previewFile(item.cart_item.file.id)">预览</button>
						</view>
					</view>
				</view>
			</view>

			<!-- 价格明细 -->
			<view class="price-section section-card">
				<view class="section-title">价格明细</view>
				<view class="price-list">
					<view class="price-item">
						<text class="price-label">文件总页数：</text>
						<text class="price-value">{{ getTotalPages() }}页</text>
					</view>
					<view class="price-item">
						<text class="price-label">文件数量：</text>
						<text class="price-value">{{ order.order_items.length }}个</text>
					</view>
					<view class="price-item total-price">
						<text class="price-label">订单总价：</text>
						<text class="price-value price-highlight">￥{{ order.total_fee.toFixed(2) }}</text>
					</view>
				</view>
			</view>
		</block>

		<view v-else class="empty-box">
			<image src="/static/icons/empty-data.png" mode="aspectFit" class="empty-image"></image>
			<text class="empty-text">订单不存在或已被删除</text>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-bar" v-if="order">
			<view class="price-info">
				<text>总计：</text>
				<text class="price-value">￥{{ order.total_fee.toFixed(2) }}</text>
			</view>
			<view class="action-buttons">
				<button
					class="action-btn pay-btn"
					v-if="order.status === 0"
					@tap="payOrder"
				>立即支付</button>
				<button
					class="action-btn cancel-btn"
					v-if="order.status === 0"
					@tap="cancelOrder"
				>取消订单</button>
				<button
					class="action-btn contact-btn"
					@tap="contactStore"
				>联系商家</button>
			</view>
		</view>
	</view>
</template>

<script>
import { getOrderDetail, updateOrderStatus, printOrder } from '@/api/index';
import { getFileIcon } from '@/utils/file'; // 导入工具函数


export default {
	data() {
		return {
			orderId: null,
			userId: 0,
			order: null,
			loading: true,
			loadingText: {
				contentdown: '上拉显示更多',
				contentrefresh: '正在加载...',
				contentnomore: '没有更多数据了'
			}
		}
	},
	onLoad(options) {
		if (options.id) {
			this.orderId = parseInt(options.id);
			// 从缓存或全局状态获取用户ID
			this.userId = this.getUserId();
			this.loadOrderDetail();
		} else {
			uni.showToast({
				title: '参数错误',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	computed: {
        getFileIcon() {
            return getFileIcon;
        }
    },
	methods: {
		// 获取用户ID的方法，根据实际登录系统修改
		getUserId() {
			// 这里应该从全局状态或存储中获取用户ID
			console.log('获取用户ID');
			return 1; // 测试用，实际应从用户系统获取
		},

		// 加载订单详情
		async loadOrderDetail() {
			console.log('加载订单详情，订单ID:', this.orderId);
			try {
				this.loading = true;
				const res = await getOrderDetail(this.orderId, this.userId);

				if (res && res.code === 0 && res.data) {
					console.log('订单详情获取成功:', res.data);
					this.order = res.data;
				} else {
					console.error('获取订单详情失败:', res);
					uni.showToast({
						title: res.message || '获取订单详情失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载订单详情出错:', error);
				uni.showToast({
					title: '网络错误，请稍后重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 获取订单状态文本
		getStatusText(status) {
			const statusMap = {
				0: '待支付',
				1: '已支付',
				2: '打印中',
				3: '已完成',
				4: '已取消',
				5: '已退款'
			};
			return statusMap[status] || '未知状态';
		},

		// 获取订单状态描述
		getStatusDesc(status) {
			const descMap = {
				0: '请尽快完成支付，以免订单自动取消',
				1: '订单已支付，等待商家处理',
				2: '文件正在打印中，请耐心等待',
				3: '文件已打印完成，请前往商家自取',
				4: '订单已取消',
				5: '订单已退款至您的账户'
			};
			return descMap[status] || '';
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			const date = new Date(timeStr);
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},

		// 格式化文件大小
		formatFileSize(size) {
			if (!size) return '0KB';

			if (size < 1024) {
				return size + 'B';
			} else if (size < 1024 * 1024) {
				return (size / 1024).toFixed(2) + 'KB';
			} else if (size < 1024 * 1024 * 1024) {
				return (size / (1024 * 1024)).toFixed(2) + 'MB';
			} else {
				return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
			}
		},

		// 格式化打印配置
		formatConfig(configStr) {
			try {
				if (!configStr) return '';

				const config = typeof configStr === 'string' ? JSON.parse(configStr) : configStr;
				const parts = [];

				// 份数
				if (config.copies) {
					parts.push(`${config.copies}份`);
				}

				// 纸型
				if (config.paperSize) {
					parts.push(config.paperSize);
				}

				// 颜色模式
				if (config.colorMode) {
					parts.push(config.colorMode === 'color' ? '彩色' : '黑白');
				}

				// 单双面
				if (config.sideMode) {
					parts.push(config.sideMode === 'double' ? '双面' : '单面');
				} else if (config.doubleSided !== undefined) {
					parts.push(config.doubleSided ? '双面' : '单面');
				}

				// 多页合一
				if (config.pagesPerSheet && config.pagesPerSheet > 1) {
					parts.push(`${config.pagesPerSheet}页合1`);
				}

				// 页面范围
				if (config.startPage && config.endPage) {
					parts.push(`第${config.startPage}-${config.endPage}页`);
				}

				return parts.join(' / ');
			} catch (error) {
				console.error('格式化打印配置出错:', error);
				return '';
			}
		},

		// 预览文件
		previewFile(fileId) {
			console.log('预览文件:', fileId);
			uni.showToast({
				title: '文件预览功能开发中',
				icon: 'none'
			});
		},

		// 拨打商家电话
		callStore(phone) {
			if (!phone) return;

			console.log('拨打商家电话:', phone);
			uni.makePhoneCall({
				phoneNumber: phone,
				fail: (err) => {
					console.error('拨打电话失败:', err);
				}
			});
		},

		// 联系商家
		contactStore() {
			if (this.order && this.order.store && this.order.store.phone) {
				this.callStore(this.order.store.phone);
			} else {
				uni.showToast({
					title: '未找到商家联系方式',
					icon: 'none'
				});
			}
		},

		// 取消订单
		cancelOrder() {
			console.log('取消订单:', this.orderId);

			uni.showModal({
				title: '取消订单',
				content: '确定要取消该订单吗？',
				confirmColor: '#1aad19',
				success: (res) => {
					if (res.confirm) {
						this.doCancelOrder();
					}
				}
			});
		},

		// 执行取消订单操作
		async doCancelOrder() {
			try {
				uni.showLoading({
					title: '取消中...'
				});

				const res = await updateOrderStatus(this.orderId, this.userId, 4); // 4表示已取消

				if (res && res.code === 0) {
					uni.showToast({
						title: '订单已取消',
						icon: 'success'
					});

					// 更新本地订单状态
					if (this.order) {
						this.order.status = 4;
					}
				} else {
					uni.showToast({
						title: res.message || '取消订单失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('取消订单出错:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 支付订单
		async payOrder() {
			console.log('支付订单:', this.orderId);
			try {
				uni.showLoading({
					title: '支付处理中...'
				});

				// 这里应该调用实际的支付API
				// 模拟支付成功
				const paymentSuccess = true;

				if (paymentSuccess) {
					// 1. 更新订单状态为已支付(状态码1)
					const updateRes = await updateOrderStatus(this.orderId, this.userId, 1);

					if (updateRes && updateRes.code === 0) {
						// 更新本地订单状态
						if (this.order) {
							this.order.status = 1;
						}

						// 2. 调用打印订单API
						const printRes = await printOrder(this.orderId, this.userId);

						if (printRes && printRes.code === 0) {
							uni.showToast({
								title: '已支付，打印中',
								icon: 'success'
							});

							// 更新本地订单状态为打印中
							if (this.order) {
								this.order.status = 2; // 2表示打印中
							}
						} else {
							uni.showToast({
								title: printRes.message || '打印失败',
								icon: 'none'
							});
						}
					} else {
						uni.showToast({
							title: updateRes.message || '支付失败',
							icon: 'none'
						});
					}
				} else {
					uni.showToast({
						title: '支付失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('支付订单出错:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				uni.hideLoading();
			}
		},

		// 计算文件总页数
		getTotalPages() {
			if (!this.order || !this.order.order_items) return 0;

			return this.order.order_items.reduce((total, item) => {
				return total + (item.cart_item.file.page_count || 0);
			}, 0);
		}
	}
}
</script>

<style lang="scss">
.detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx; // 为底部操作栏留出空间
}

.loading-box, .empty-box {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.section-card {
	background-color: #fff;
	border-radius: 16rpx;
	margin: 20rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
	padding-left: 20rpx;

	&:before {
		content: '';
		position: absolute;
		left: 0;
		top: 8rpx;
		width: 6rpx;
		height: 28rpx;
		background-color: #1aad19;
		border-radius: 3rpx;
	}
}

// 订单状态
.status-section {
	background-color: #1aad19;
	color: #fff;
	padding: 40rpx 30rpx;

	.status-text {
		font-size: 36rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.status-desc {
		font-size: 26rpx;
		opacity: 0.9;
	}
}

// 商家信息
.store-info {
	.store-name {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.store-address {
		font-size: 26rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.store-phone {
		font-size: 26rpx;
		color: #666;
		display: flex;
		align-items: center;

		.phone-label {
			color: #999;
		}

		.phone-value {
			color: #333;
		}

		.phone-icon {
			margin-left: auto;
			background-color: #f0f0f0;
			color: #666;
			padding: 6rpx 20rpx;
			border-radius: 20rpx;
			font-size: 24rpx;
		}
	}
}

// 订单信息
.order-info {
	.info-item {
		display: flex;
		font-size: 26rpx;
		margin-bottom: 16rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.info-label {
			color: #999;
			width: 160rpx;
		}

		.info-value {
			color: #333;
			flex: 1;
		}
	}
}

// 文件列表
.files-list {
	.file-item {
		display: flex;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f5f5f5;

		&:last-child {
			border-bottom: none;
			padding-bottom: 0;
		}

		.file-left {
			width: 80rpx;
			margin-right: 20rpx;

			.file-icon {
				width: 80rpx;
				height: 80rpx;
			}
		}

		.file-center {
			flex: 1;
			padding-right: 20rpx;

			.file-name {
				font-size: 28rpx;
				color: #333;
				margin-bottom: 8rpx;
				word-break: break-all;
				line-height: 1.4;
				padding-right: 20rpx;
			}

			.file-info {
				display: flex;
				font-size: 24rpx;
				color: #999;
				margin-bottom: 8rpx;
				.file-pages {
					margin-right: 20rpx;
				}
			}

			.file-config {
				font-size: 24rpx;
				color: #666;
				display: inline-block;
				margin-top: 8rpx;
				line-height: 1.4;
				max-width: 100%;
				box-sizing: border-box;
			}
		}

		.file-right {
			display: flex;
			flex-direction: column;
			align-items: flex-end;
			justify-content: space-between;

			.file-price {
				font-size: 28rpx;
				color: #ff6b00;
				font-weight: bold;
			}

			.file-preview {
				font-size: 22rpx;
				color: #1aad19;
				border: 1rpx solid #1aad19;
				padding: 0 20rpx;
				height: 50rpx;
				line-height: 46rpx;
				border-radius: 25rpx;
				background-color: #fff;
			}
		}
	}
}

// 价格明细
.price-list {
	.price-item {
		display: flex;
		justify-content: space-between;
		font-size: 26rpx;
		margin-bottom: 16rpx;

		.price-label {
			color: #666;
		}

		.price-value {
			color: #333;
			font-weight: bold;
		}
	}

	.total-price {
		padding-top: 16rpx;
		border-top: 1rpx dashed #eee;
		margin-top: 16rpx;

		.price-label {
			font-size: 30rpx;
		}

		.price-highlight {
			color: #ff6b00;
			font-size: 32rpx;
		}
	}
}

// 底部操作栏
.bottom-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	z-index: 100;
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);

	.price-info {
		font-size: 28rpx;
		color: #333;

		.price-value {
			color: #ff6b00;
			font-size: 36rpx;
			font-weight: bold;
			margin-left: 10rpx;
		}
	}

	.action-buttons {
		display: flex;

		.action-btn {
			margin-left: 20rpx;
			height: 70rpx;
			line-height: 70rpx;
			padding: 0 30rpx;
			border-radius: 35rpx;
			font-size: 28rpx;
			background-color: #fff;
		}

		.cancel-btn {
			border: 1rpx solid #ccc;
			color: #666;
		}

		.pay-btn {
			background-color: #1aad19;
			color: white;
		}

		.contact-btn {
			border: 1rpx solid #1aad19;
			color: #1aad19;
		}
	}
}
</style>