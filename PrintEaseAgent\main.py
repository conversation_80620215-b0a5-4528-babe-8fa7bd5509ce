import sys
import os
import logging

# 确保可以导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from PyQt5.QtWidgets import QApplication
from ui.main_window import MainWindow
from utils.log_utils import setup_logging
from utils.config_manager import ConfigManager

def main():
    # 初始化日志
    setup_logging(log_dir="logs", log_level=logging.INFO)
    logger = logging.getLogger(__name__)
    logger.info("PrintEase代理程序启动")
    
    # 加载配置
    config_manager = ConfigManager()
    
    # 创建必要的目录
    uploads_dir = config_manager.get_uploads_dir()
    os.makedirs(uploads_dir, exist_ok=True)
    logger.info(f"已创建上传目录: {uploads_dir}")
    
    temp_dir = config_manager.config.get("temp_dir", "temp")
    os.makedirs(temp_dir, exist_ok=True)
    logger.info(f"已创建临时目录: {temp_dir}")
    
    # 创建应用
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    logger.info("应用程序已启动")
    exit_code = app.exec_()
    logger.info(f"应用程序退出，退出码: {exit_code}")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()