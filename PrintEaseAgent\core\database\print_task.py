from typing import Optional, Dict, Any, List
from .base import BaseManager, logger

class PrintTaskManager(BaseManager):
    """打印任务数据库管理类"""

    def _create_tables(self):
        """创建打印任务相关表"""
        if not self.connection:
            raise Exception("数据库未连接")

        # 创建打印任务表
        self.connection.execute("""
        CREATE TABLE IF NOT EXISTS print_tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id TEXT NOT NULL UNIQUE,         -- 任务ID (task_orderID_itemID)
            order_id INTEGER NOT NULL,            -- 订单ID
            order_item_id INTEGER NOT NULL,       -- 订单项ID
            file_id INTEGER NOT NULL,             -- 文件ID
            filename TEXT NOT NULL,               -- 文件名
            file_type TEXT NOT NULL,              -- 文件类型
            file_size INTEGER NOT NULL,           -- 文件大小(bytes)
            page_count INTEGER NOT NULL,          -- 页数
            download_url TEXT NOT NULL,           -- 文件下载URL
            local_path TEXT,                      -- 本地文件路径
            print_config TEXT NOT NULL,           -- 打印配置(JSON)
            printer_id INTEGER,                   -- 分配的打印机ID
            status TEXT NOT NULL,                 -- 任务状态(waiting/downloading/pending/printing/completed/failed/cancelled)
            error_message TEXT,                   -- 错误信息
            retry_count INTEGER DEFAULT 0,        -- 重试次数
            progress INTEGER DEFAULT 0,           -- 打印进度(0-100)
            created_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),  -- 创建时间
            updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),  -- 更新时间
            started_at TIMESTAMP,                 -- 开始打印时间
            completed_at TIMESTAMP                -- 完成时间
        )
        """)

        # 检查并删除已存在的触发器
        self.connection.execute("""
        DROP TRIGGER IF EXISTS update_print_tasks_timestamp;
        """)

        # 创建任务状态更新触发器
        self.connection.execute("""
        CREATE TRIGGER update_print_tasks_timestamp
        AFTER UPDATE ON print_tasks
        FOR EACH ROW
        BEGIN
            UPDATE print_tasks SET updated_at = datetime('now', 'localtime')
            WHERE id = old.id;
        END;
        """)

        # 创建索引
        self.connection.execute("CREATE INDEX IF NOT EXISTS idx_print_tasks_order_id ON print_tasks(order_id)")
        self.connection.execute("CREATE INDEX IF NOT EXISTS idx_print_tasks_order_item_id ON print_tasks(order_item_id)")
        self.connection.execute("CREATE INDEX IF NOT EXISTS idx_print_tasks_status ON print_tasks(status)")
        self.connection.execute("CREATE INDEX IF NOT EXISTS idx_print_tasks_printer_id ON print_tasks(printer_id)")

        self.connection.commit()
        logger.info("打印任务表创建成功")

    def add_task(self, task_id: str, order_id: int, order_item_id: int,
                 file_id: int, filename: str, file_type: str,
                 file_size: int, page_count: int, download_url: str,
                 print_config: str) -> int:
        """添加打印任务"""
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO print_tasks (
                    task_id, order_id, order_item_id, file_id, filename,
                    file_type, file_size, page_count, download_url, print_config,
                    status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'waiting', datetime('now', 'localtime'), datetime('now', 'localtime'))
            """, (task_id, order_id, order_item_id, file_id, filename,
                  file_type, file_size, page_count, download_url, print_config))
            self.connection.commit()
            return cursor.lastrowid
        except Exception as e:
            logger.error(f"添加打印任务失败: {e}")
            raise
        finally:
            self.close()

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取打印任务"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                WHERE task_id = ?
            """, (task_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_tasks_by_status(self, status: str) -> List[Dict[str, Any]]:
        """获取指定状态的打印任务列表"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                WHERE status = ?
                ORDER BY created_at ASC
            """, (status,))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def update_task_status(self, task_id: str, status: str,
                          error_message: Optional[str] = None,
                          progress: Optional[int] = None) -> bool:
        """更新任务状态"""
        try:
            self.connect()
            update_fields = ["status = ?"]
            params = [status]

            if error_message is not None:
                update_fields.append("error_message = ?")
                params.append(error_message)

            if progress is not None:
                update_fields.append("progress = ?")
                params.append(progress)

            if status == "printing":
                update_fields.append("started_at = datetime('now', 'localtime')")

            elif status in ["completed", "failed", "cancelled"]:
                update_fields.append("completed_at = datetime('now', 'localtime')")

            sql = f"""
                UPDATE print_tasks
                SET {', '.join(update_fields)}
                WHERE task_id = ?
            """
            self.connection.execute(sql, params + [task_id])
            self.connection.commit()
            return True
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            return False
        finally:
            self.close()

    def update_task_local_path(self, task_id: str, local_path: str) -> bool:
        """更新任务本地文件路径"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE print_tasks
                SET local_path = ?
                WHERE task_id = ?
            """, (local_path, task_id))
            self.connection.commit()
            return True
        except Exception as e:
            logger.error(f"更新任务本地文件路径失败: {e}")
            return False
        finally:
            self.close()

    def update_task_printer(self, task_id: str, printer_id: int) -> bool:
        """更新任务的打印机ID

        Args:
            task_id: 任务ID
            printer_id: 打印机ID

        Returns:
            bool: 是否成功更新
        """
        try:
            self.connect()
            self.connection.execute("""
                UPDATE print_tasks
                SET printer_id = ?
                WHERE task_id = ?
            """, (printer_id, task_id))
            self.connection.commit()
            return True
        except Exception as e:
            logger.error(f"更新任务打印机失败: {e}")
            return False
        finally:
            self.close()

    def increment_retry_count(self, task_id: str) -> bool:
        """增加重试次数"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE print_tasks
                SET retry_count = retry_count + 1
                WHERE task_id = ?
            """, (task_id,))
            self.connection.commit()
            return True
        except Exception as e:
            logger.error(f"更新重试次数失败: {e}")
            return False
        finally:
            self.close()

    def get_printer_tasks(self, printer_id: int) -> List[Dict[str, Any]]:
        """获取指定打印机的任务列表"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                WHERE printer_id = ?
                ORDER BY created_at DESC
            """, (printer_id,))
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def get_pending_tasks(self) -> List[Dict[str, Any]]:
        """获取所有等待中的任务"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                WHERE status = 'waiting'
                ORDER BY created_at ASC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """获取所有活动任务（等待中或打印中）"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                WHERE status IN ('waiting', 'printing')
                ORDER BY created_at ASC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def clean_completed_tasks(self, days: int = 7) -> int:
        """清理指定天数前的已完成任务"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                DELETE FROM print_tasks
                WHERE status IN ('completed', 'failed', 'cancelled')
                AND completed_at < datetime('now', ?)
            """, (f'-{days} days',))
            self.connection.commit()
            return cursor.rowcount
        finally:
            self.close()

    def delete_task(self, task_id: str) -> bool:
        """删除指定的打印任务"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                DELETE FROM print_tasks
                WHERE task_id = ?
            """, (task_id,))
            self.connection.commit()
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"删除打印任务失败: {e}")
            return False
        finally:
            self.close()

    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """获取所有打印任务"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM print_tasks
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()
