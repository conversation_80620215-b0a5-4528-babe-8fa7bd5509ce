import platform
import uuid
import socket
import logging
import wmi

logger = logging.getLogger(__name__)

def get_device_info():
    """
    获取设备信息
    :return: (device_id, device_name)
    """
    try:
        # 获取设备唯一标识
        w = wmi.WMI()
        # 使用主板序列号作为设备唯一标识
        for board in w.Win32_BaseBoard():
            device_id = board.SerialNumber.strip()
            break
        else:
            # 如果获取不到主板序列号,使用MAC地址
            device_id = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                for elements in range(0,2*6,2)][::-1])
        
        # 获取设备名称(计算机名)
        device_name = platform.node()
        
        logger.info(f"获取设备信息成功: device_id={device_id}, device_name={device_name}")
        return device_id, device_name
    except Exception as e:
        logger.error(f"获取设备信息失败: {str(e)}")
        # 如果获取失败,使用备选方案
        device_id = str(uuid.getnode())  # 使用MAC地址的数字形式
        device_name = socket.gethostname()  # 使用主机名
        return device_id, device_name 