package services

import (
	"PrintEaseBackend/internal/auth"
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"

	"errors"
	"fmt"
	"log"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// MerchantService 商家服务
type MerchantService struct{}

// GetMerchantByID 根据ID获取商家
func (s *MerchantService) GetMerchantByID(id int64) (*models.Merchant, error) {
	var merchant models.Merchant
	result := database.DB.First(&merchant, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &merchant, nil
}

// GetMerchantByUsername 根据用户名获取商家
func (s *MerchantService) GetMerchantByUsername(username string) (*models.Merchant, error) {
	var merchant models.Merchant
	result := database.DB.Where("username = ?", username).First(&merchant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &merchant, nil
}

// GetMerchantByPhone 根据手机号获取商家
func (s *MerchantService) GetMerchantByPhone(phone string) (*models.Merchant, error) {
	var merchant models.Merchant
	result := database.DB.Where("phone = ?", phone).First(&merchant)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, result.Error
	}
	return &merchant, nil
}

// CreateMerchant 创建商家
func (s *MerchantService) CreateMerchant(req *models.MerchantCreateReq) (*models.Merchant, error) {
	// 检查用户名是否已存在
	existMerchant, err := s.GetMerchantByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	if existMerchant != nil {
		return nil, errors.New("用户名已存在")
	}

	// 如果提供了手机号，检查手机号是否已存在
	if req.Phone != nil && *req.Phone != "" {
		existMerchant, err = s.GetMerchantByPhone(*req.Phone)
		if err != nil {
			return nil, err
		}
		if existMerchant != nil {
			return nil, errors.New("手机号已存在")
		}
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建商家
	merchant := &models.Merchant{
		Username:     req.Username,
		Password:     string(hashedPassword),
		RealName:     req.RealName,
		Phone:        req.Phone,
		Email:        req.Email,
		StoreID:      req.StoreID,
		MerchantType: req.MerchantType,
		Status:       models.MerchantStatusEnabled,
		Avatar:       req.Avatar,
	}

	if err := database.DB.Create(merchant).Error; err != nil {
		return nil, err
	}

	return merchant, nil
}

// UpdateMerchant 更新商家
func (s *MerchantService) UpdateMerchant(id int64, req *models.MerchantUpdateReq) error {
	// 检查商家是否存在
	merchant, err := s.GetMerchantByID(id)
	if err != nil {
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 如果提供了手机号，检查手机号是否已被其他商家使用
	if req.Phone != nil && *req.Phone != "" && (merchant.Phone == nil || *merchant.Phone != *req.Phone) {
		existMerchant, err := s.GetMerchantByPhone(*req.Phone)
		if err != nil {
			return err
		}
		if existMerchant != nil && existMerchant.ID != id {
			return errors.New("手机号已存在")
		}
	}

	// 更新商家信息
	updates := make(map[string]interface{})
	if req.RealName != nil {
		updates["real_name"] = req.RealName
	}
	if req.Phone != nil {
		updates["phone"] = req.Phone
	}
	if req.Email != nil {
		updates["email"] = req.Email
	}
	if req.Status != nil {
		updates["status"] = req.Status
	}
	if req.Avatar != nil {
		updates["avatar"] = req.Avatar
	}

	return database.DB.Model(&models.Merchant{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteMerchant 删除商家
func (s *MerchantService) DeleteMerchant(id int64) error {
	return database.DB.Delete(&models.Merchant{}, id).Error
}

// ListMerchants 查询商家列表
func (s *MerchantService) ListMerchants(query *models.MerchantQueryReq) ([]models.Merchant, int64, error) {
	db := database.DB.Model(&models.Merchant{})

	// 添加查询条件
	if query.Username != "" {
		db = db.Where("username LIKE ?", "%"+query.Username+"%")
	}
	if query.RealName != "" {
		db = db.Where("real_name LIKE ?", "%"+query.RealName+"%")
	}
	if query.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+query.Phone+"%")
	}
	if query.StoreID != 0 {
		db = db.Where("store_id = ?", query.StoreID)
	}
	if query.MerchantType != "" {
		db = db.Where("merchant_type = ?", query.MerchantType)
	}
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}

	// 查询总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	var merchants []models.Merchant
	offset := (query.Page - 1) * query.PageSize
	if err := db.Offset(int(offset)).Limit(int(query.PageSize)).Order("id DESC").Find(&merchants).Error; err != nil {
		return nil, 0, err
	}

	return merchants, total, nil
}

// Login 商家登录
func (s *MerchantService) Login(req *models.MerchantLoginReq, ip string) (*models.MerchantLoginResp, error) {
	// 根据用户名获取商家
	merchant, err := s.GetMerchantByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	if merchant == nil {
		return nil, errors.New("账号或者密码错误！")
	}

	// 检查商家状态
	if merchant.Status != models.MerchantStatusEnabled {
		return nil, errors.New("账号已被禁用")
	}

	// 验证密码
	err = bcrypt.CompareHashAndPassword([]byte(merchant.Password), []byte(req.Password))
	if err != nil {
		return nil, errors.New("账号或者密码错误！")
	}

	// 确定商家角色
	var roleType auth.RoleType
	if merchant.MerchantType == models.MerchantTypeOwner {
		roleType = auth.RoleMerchantOwner
	} else {
		roleType = auth.RoleMerchantStaff
	}

	// 确定客户端类型
	clientType := auth.ClientTypeWeb // 默认为web客户端
	if req.ClientType == string(auth.ClientTypeAgent) {
		clientType = auth.ClientTypeAgent
	}

	// 使用JWT管理器生成令牌
	jwtManager := auth.NewJWTManager(config.GlobalConfig.Auth)

	// 获取商店名称
	var storeName string
	if merchant.StoreID != 0 {
		storeService := &StoreService{}
		store, err := storeService.GetStoreByID(uint64(merchant.StoreID))
		if err != nil {
			// 记录错误但不影响登录流程
			log.Printf("获取商店信息失败: %v", err)
			storeName = ""
		} else if store != nil {
			storeName = store.Name
		}
	}

	token, err := jwtManager.GenerateToken(
		merchant.ID,
		merchant.Username,
		merchant.StoreID,
		storeName,
		clientType,
		roleType,
	)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %v", err)
	}

	// 更新登录信息
	now := time.Now()
	if err := database.DB.Model(merchant).Updates(map[string]interface{}{
		"last_login_time": now,
		"last_login_ip":   ip,
	}).Error; err != nil {
		// 记录错误但不影响登录结果
		log.Printf("更新登录信息失败: %v", err)
	}

	return &models.MerchantLoginResp{
		Token:    token,
		Merchant: *merchant,
	}, nil
}

// ChangePassword 修改密码
func (s *MerchantService) ChangePassword(id int64, req *models.MerchantChangePasswordReq) error {
	// 获取商家信息
	merchant, err := s.GetMerchantByID(id)
	if err != nil {
		return err
	}
	if merchant == nil {
		return errors.New("商家不存在")
	}

	// 验证旧密码
	err = bcrypt.CompareHashAndPassword([]byte(merchant.Password), []byte(req.OldPassword))
	if err != nil {
		return errors.New("旧密码错误")
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	// 更新密码
	return database.DB.Model(merchant).Update("password", string(hashedPassword)).Error
}
