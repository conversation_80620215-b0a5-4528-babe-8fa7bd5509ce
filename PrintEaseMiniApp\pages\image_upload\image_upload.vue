async chooseImageSource(source) {
  this.hideAddPopup()
  let res
  try {
    const oldLen = this.imageList.length
    if (source === 'chat') {
      res = await uni.chooseMessageFile({ count: 9, type: 'image' })
    } else if (source === 'album') {
      res = await uni.chooseImage({ count: 9, sourceType: ['album'] })
    } else if (source === 'camera') {
      res = await uni.chooseImage({ count: 1, sourceType: ['camera'] })
    }
    if (res && res.tempFiles) {
      const newImages = res.tempFiles.map(f => ({
        id: Date.now() + Math.random(),
        path: f.path
      }))
      this.imageList = this.imageList.concat(newImages)
      // 滚动到新图片
      this.$nextTick(() => {
        if (newImages.length > 0) {
          const firstNewId = newImages[0].id
          uni.createSelectorQuery()
            .select(`#img-${firstNewId}`)
            .boundingClientRect(rect => {
              if (rect) {
                uni.pageScrollTo({
                  scrollTop: rect.top + uni.getSystemInfoSync().windowScrollY - 20, // 适当偏移
                  duration: 300
                })
              }
            })
            .exec()
        }
      })
    }
  } catch (e) {
    if (e.errMsg && e.errMsg.includes('cancel')) return
    uni.showToast({ title: '选择图片失败', icon: 'none' })
  }
}async chooseImageSource(source) {
  this.hideAddPopup()
  let res
  try {
    const oldLen = this.imageList.length
    if (source === 'chat') {
      res = await uni.chooseMessageFile({ count: 9, type: 'image' })
    } else if (source === 'album') {
      res = await uni.chooseImage({ count: 9, sourceType: ['album'] })
    } else if (source === 'camera') {
      res = await uni.chooseImage({ count: 1, sourceType: ['camera'] })
    }
    if (res && res.tempFiles) {
      const newImages = res.tempFiles.map(f => ({
        id: Date.now() + Math.random(),
        path: f.path
      }))
      this.imageList = this.imageList.concat(newImages)
      // 滚动到新图片
      this.$nextTick(() => {
        if (newImages.length > 0) {
          const firstNewId = newImages[0].id
          uni.createSelectorQuery()
            .select(`#img-${firstNewId}`)
            .boundingClientRect(rect => {
              if (rect) {
                uni.pageScrollTo({
                  scrollTop: rect.top + uni.getSystemInfoSync().windowScrollY - 20, // 适当偏移
                  duration: 300
                })
              }
            })
            .exec()
        }
      })
    }
  } catch (e) {
    if (e.errMsg && e.errMsg.includes('cancel')) return
    uni.showToast({ title: '选择图片失败', icon: 'none' })
  }
}<template>
    <view class="container">
      <!-- 图片列表区 -->
      <view class="image-list">
        <view v-for="(img, idx) in imageList" :key="img.id" :id="'img-' + img.id" class="image-item">
          <text class="img-index">{{ idx + 1 }}</text>
          <view class="img-box">
            <image :src="img.path" class="preview-img" mode="aspectFit"></image>
          </view>
          <view class="img-ops">
            <button class="edit-btn" @click="editImage(idx)">编辑</button>
            <button class="move-btn" @click="moveUp(idx)" :disabled="idx === 0">上移</button>
            <button class="move-btn" @click="moveDown(idx)" :disabled="idx === imageList.length - 1">下移</button>
            <button class="del-btn" @click="removeImage(idx)">删除</button>
          </view>
        </view>
      </view>
  
      <!-- 底部操作区 -->
      <view class="footer-bar">
        <button class="footer-btn" @click="showAddPopup">继续添加</button>
        <button class="footer-btn" @click="handlePreview" :loading="previewLoading">预览</button>
        <button class="footer-btn primary" @click="handleSubmit" :loading="submitLoading">确认提交</button>
      </view>
  
      <!-- 选择图片来源弹窗 -->
      <uni-popup ref="addPopup" type="bottom">
        <view class="popup-content">
          <view class="popup-item" @click="chooseImageSource('chat')">从微信聊天中选择</view>
          <view class="popup-item" @click="chooseImageSource('album')">从相册选择</view>
          <view class="popup-item" @click="chooseImageSource('camera')">拍摄</view>
          <view class="popup-item cancel" @click="hideAddPopup">取消</view>
        </view>
      </uni-popup>
    </view>
</template>
  
<script>
  import uniPopup from '@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue'
  import { mapGetters, mapMutations } from 'vuex'
  // 这里假设有后端API方法 generatePdf(images) 返回 { url, fileName }
  
  export default {
    components: { uniPopup },
    computed: {
      ...mapGetters(['getImages']),
      imageList: {
        get() { 
            // 日志打印图片信息
            console.log('图片信息:', this.getImages)
            return this.getImages 
        },
        set(val) { this.updateImages(val) }
      },
      lastPdfInfo: {
        get() { return this.$store.state.lastPdfInfo },
        set(val) { this.$store.dispatch('setLastPdfInfo', val) }
      },
      previewLoading: {
        get() { return this.$store.state.previewLoading },
        set(val) { this.$store.dispatch('setPreviewLoading', val) }
      },
      submitLoading: {
        get() { return this.$store.state.submitLoading },
        set(val) { this.$store.dispatch('setSubmitLoading', val) }
      },
    },
    methods: {
      ...mapMutations(['removeImage', 'updateImages']),
      // 编辑图片（UI占位）
      editImage(idx) {
        uni.showToast({ title: '编辑功能暂未实现', icon: 'none' })
      },
      // 上移
      moveUp(idx) {
        if (idx > 0) {
          const arr = [...this.imageList]
          const temp = arr[idx - 1]
          arr[idx - 1] = arr[idx]
          arr[idx] = temp
          this.updateImages([...arr])
        }
      },
      // 下移
      moveDown(idx) {
        if (idx < this.imageList.length - 1) {
          const arr = [...this.imageList]
          const temp = arr[idx + 1]
          arr[idx + 1] = arr[idx]
          arr[idx] = temp
          this.updateImages([...arr])
        }
      },
      removeImage(idx) {
        const arr = [...this.imageList]
        arr.splice(idx, 1)
        this.updateImages(arr)
      },
      // 显示添加图片弹窗
      showAddPopup() {
        this.$refs.addPopup.open()
      },
      hideAddPopup() {
        this.$refs.addPopup.close()
      },
      // 选择图片来源
      async chooseImageSource(source) {
        this.hideAddPopup()
        let res
        try {
          if (source === 'chat') {
            res = await uni.chooseMessageFile({ count: 9, type: 'image' })
          } else if (source === 'album') {
            res = await uni.chooseImage({ count: 9, sourceType: ['album'] })
          } else if (source === 'camera') {
            res = await uni.chooseImage({ count: 1, sourceType: ['camera'] })
          }
          if (res && res.tempFiles) {
            const newImages = res.tempFiles.map(f => ({
              id: Date.now() + Math.random(),
              path: f.path
            }))
            this.imageList = this.imageList.concat(newImages)
            // 滚动到新图片
            this.$nextTick(() => {
              if (newImages.length > 0) {
                const firstNewId = newImages[0].id
                uni.createSelectorQuery()
                  .select(`#img-${firstNewId}`)
                  .boundingClientRect(rect => {
                    if (rect) {
                      uni.pageScrollTo({
                        scrollTop: rect.top + uni.getSystemInfoSync().windowScrollY - 20, // 适当偏移
                        duration: 300
                      })
                    }
                })
                .exec()
              }
            })
          }
        } catch (e) {
          if (e.errMsg && e.errMsg.includes('cancel')) return
          uni.showToast({ title: '选择图片失败', icon: 'none' })
        }
      },
      // 预览
      async handlePreview() {
        if (!this.imageList.length) {
          uni.showToast({ title: '请先添加图片', icon: 'none' })
          return
        }
        this.previewLoading = true
        try {
          // TODO: 调用后端生成PDF接口，传递图片列表
          // const pdfInfo = await generatePdf(this.imageList)
          // 模拟返回
          const pdfInfo = {
            url: '/static/mock/preview.pdf',
            fileName: `${this.imageList.length}张图片.pdf`
          }
          this.lastPdfInfo = pdfInfo
          // 打开PDF预览
          uni.navigateTo({ url: `/pages/pdf-preview/pdf-preview?url=${encodeURIComponent(pdfInfo.url)}` })
        } finally {
          this.previewLoading = false
        }
      },
      // 确认提交
      async handleSubmit() {
        if (!this.imageList.length) {
          uni.showToast({ title: '请先添加图片', icon: 'none' })
          return
        }
        this.submitLoading = true
        try {
          let pdfInfo = this.lastPdfInfo
          if (!pdfInfo) {
            // 没有预览过，先生成一次PDF
            // pdfInfo = await generatePdf(this.imageList)
            pdfInfo = {
              url: '/static/mock/preview.pdf',
              fileName: `${this.imageList.length}张图片.pdf`
            }
            this.lastPdfInfo = pdfInfo
          }
          // 跳转到cart页面，携带pdf信息
          uni.navigateTo({
            url: `/pages/cart/cart?pdfUrl=${encodeURIComponent(pdfInfo.url)}&pdfName=${encodeURIComponent(pdfInfo.fileName)}`
          })
        } finally {
          this.submitLoading = false
        }
      }
    }
  }
</script>
  
<style lang="scss">
  @import "@/uni.scss";
  .container {
    min-height: 100vh;
    background: #f8f8f8;
    /* 让页面本身滚动 */
    overflow-y: auto;
  }
  .custom-nav {
    background: #fff;
    .nav-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      .nav-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  .image-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    padding: 20rpx;
    padding-bottom: 160rpx; // 预留底部操作栏高度
    .image-item {
      width: 45vw;
      margin-bottom: 20rpx;
      .img-index { font-size: 28rpx; color: #059669; }

      // A4比例图片容器
      .img-box {
        width: 100%;
        position: relative;
        // 高度 = 宽度 * 1.414
        padding-bottom: 141.4%;
        background: #fff;
        border-radius: 12rpx;
        box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.05);
        overflow: hidden;
        .preview-img {
          position: absolute;
          left: 0; top: 0; width: 100%; height: 100%;
          object-fit: contain;
          border-radius: 12rpx 12rpx 0 0;
          background: #fff;
        }
      }

      // 按钮区单独放外面
      .img-ops {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 10rpx 0 0 0;
        .edit-btn, .move-btn, .del-btn {
          font-size: 24rpx;
          margin: 0 4rpx;
          background: #f5f5f5;
          border-radius: 8rpx;
          color: #333;
        }
        .move-btn:disabled,
        .move-btn[disabled] {
          color: #bbb !important;
          background: #eee !important;
          border: none !important;
          cursor: not-allowed !important;
          opacity: 0.7 !important;
        }
        .del-btn { color: #FF3B30; }
      }
    }
  }
  .footer-bar {
    position: fixed;
    left: 0; right: 0; bottom: 0;
    display: flex;
    justify-content: space-around;
    background: #fff;
    padding: 16rpx 0;
    box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.05);
    z-index: 100;
    .footer-btn {
      flex: 1;
      margin: 0 10rpx;
      height: 88rpx;
      line-height: 88rpx;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #333;
      background: #f5f5f5;
      &.primary {
        background: #4CD964;
        color: #fff;
      }
    }
  }
  .popup-content {
    background: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    .popup-item {
      height: 100rpx;
      line-height: 100rpx;
      text-align: center;
      font-size: 32rpx;
      color: #333;
      border-bottom: 1rpx solid #eee;
      &.cancel {
        margin-top: 20rpx;
        color: #999;
        border-bottom: none;
      }
    }
  }
</style>