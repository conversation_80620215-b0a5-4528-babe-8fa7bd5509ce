package handlers

import (
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"PrintEaseBackend/internal/services"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// OrderHandler 订单处理器
type OrderHandler struct {
	orderService *services.OrderService
}

// NewOrderHandler 创建新的订单处理器
func NewOrderHandler() *OrderHandler {
	return &OrderHandler{
		orderService: &services.OrderService{},
	}
}

// CreateOrder 创建订单
// @Summary 创建订单
// @Description 基于购物车项创建新订单
// @Tags 订单
// @Accept json
// @Produce json
// @Param data body services.CreateOrderRequest true "订单数据"
// @Success 200 {object} models.Order "成功"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/orders [post]
func (h *OrderHandler) CreateOrder(c *gin.Context) {
	var req services.CreateOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 调用服务创建订单
	order, err := h.orderService.CreateOrder(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "创建订单失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "订单创建成功",
		"data":    order,
	})
}

// GetOrderList 获取订单列表
// @Summary 获取订单列表
// @Description 获取当前用户的订单列表，支持分页和状态筛选
// @Tags 订单
// @Accept json
// @Produce json
// @Param user_id query int true "用户ID"
// @Param status query int false "订单状态"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页条数" default(10)
// @Success 200 {object} []models.Order "成功"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/orders [get]
func (h *OrderHandler) GetOrderList(c *gin.Context) {
	// 解析参数
	userID, err := strconv.ParseUint(c.Query("user_id"), 10, 64)
	if err != nil || userID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID参数错误",
		})
		return
	}

	// 解析状态参数（可选）
	var status *int8
	if statusStr := c.Query("status"); statusStr != "" {
		statusInt, err := strconv.ParseInt(statusStr, 10, 8)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "状态参数错误",
			})
			return
		}
		statusInt8 := int8(statusInt)
		status = &statusInt8
	}

	// 解析分页参数
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	if err != nil || pageSize < 1 {
		pageSize = 10
	}

	// 调用服务获取订单列表
	orders, total, err := h.orderService.GetOrderList(userID, status, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取订单列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取订单列表成功",
		"data": gin.H{
			"orders":     orders,
			"total":      total,
			"page":       page,
			"page_size":  pageSize,
			"total_page": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	})
}

// GetOrderDetail 获取订单详情
// @Summary 获取订单详情
// @Description 获取指定订单的详细信息
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param user_id query int true "用户ID"
// @Success 200 {object} models.Order "成功"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 404 {object} gin.H "订单不存在"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/orders/{id} [get]
func (h *OrderHandler) GetOrderDetail(c *gin.Context) {
	// 解析订单ID
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil || orderID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "订单ID参数错误",
		})
		return
	}

	// 解析用户ID
	userID, err := strconv.ParseUint(c.Query("user_id"), 10, 64)
	if err != nil || userID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "用户ID参数错误",
		})
		return
	}

	// 调用服务获取订单详情
	order, err := h.orderService.GetOrderByID(orderID, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取订单详情失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "获取订单详情成功",
		"data":    order,
	})
}

// UpdateOrderStatus 更新订单状态
// @Summary 更新订单状态
// @Description 更新指定订单的状态
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param data body map[string]interface{} true "状态数据"
// @Success 200 {object} gin.H "成功"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/orders/{id}/status [put]
func (h *OrderHandler) UpdateOrderStatus(c *gin.Context) {
	// 解析订单ID
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil || orderID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "订单ID参数错误",
		})
		return
	}

	// 解析请求参数
	var req struct {
		UserID uint64 `json:"user_id"`
		Status int8   `json:"status"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 调用服务更新订单状态
	if err := h.orderService.UpdateOrderStatus(orderID, req.UserID, req.Status); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新订单状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "更新订单状态成功",
	})
}

// PrintOrder 打印订单
// @Summary 打印订单
// @Description 将订单发送到打印机进行打印
// @Tags 订单
// @Accept json
// @Produce json
// @Param id path int true "订单ID"
// @Param data body map[string]interface{} true "用户数据"
// @Success 200 {object} gin.H "成功"
// @Failure 400 {object} gin.H "请求错误"
// @Failure 500 {object} gin.H "服务器内部错误"
// @Router /api/orders/{id}/print [post]
func (h *OrderHandler) PrintOrder(c *gin.Context) {
	// 解析订单ID
	orderID, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil || orderID == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "订单ID参数错误",
		})
		return
	}

	// 解析请求参数
	var req struct {
		UserID uint64 `json:"user_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "参数错误: " + err.Error(),
		})
		return
	}

	// 获取订单信息
	order, err := h.orderService.GetOrderByID(orderID, req.UserID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "获取订单信息失败: " + err.Error(),
		})
		return
	}

	// 检查订单状态是否为已支付
	if order.Status != 1 { // 1表示已支付
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "只有已支付的订单才能打印",
		})
		return
	}

	// 更新订单状态为打印中(2)
	if err := h.orderService.UpdateOrderStatus(orderID, req.UserID, 2); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "更新订单状态失败: " + err.Error(),
		})
		return
	}

	// 异步分发打印任务
	go h.dispatchPrintTask(order)

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "打印任务已发送",
	})
}

// 分发打印任务的方法
func (h *OrderHandler) dispatchPrintTask(order *models.Order) {
	// 重新加载订单,确保包含所有关联数据
	var completeOrder models.Order
	if err := database.DB.Preload("OrderItems").
		Preload("OrderItems.CartItem").
		Preload("OrderItems.CartItem.File").
		First(&completeOrder, order.ID).Error; err != nil {
		log.Printf("加载订单详情失败: %v", err)
		return
	}

	// 检查是否有订单项
	if len(completeOrder.OrderItems) == 0 {
		log.Printf("订单 %d 没有订单项", order.ID)
		return
	}

	// 遍历所有订单项
	for _, orderItem := range completeOrder.OrderItems {
		file := orderItem.CartItem.File

		// 构建下载URL
		baseURL := "http://127.0.0.1:8080"
		downloadURL := fmt.Sprintf("%s/api/download?file_id=%d", baseURL, file.ID)

		// 构建打印任务消息
		task := map[string]interface{}{
			"type":    "print_task",
			"task_id": fmt.Sprintf("task_%d_%d", order.ID, orderItem.ID), // 包含order_id和item_id
			"data": map[string]interface{}{
				"order_id":      order.ID,
				"order_item_id": orderItem.ID,
				"file_info": map[string]interface{}{
					"file_id":      file.ID,
					"filename":     file.Filename,
					"file_type":    file.FileType,
					"file_size":    file.FileSize,
					"page_count":   file.PageCount,
					"download_url": downloadURL,
				},
				"print_config": json.RawMessage(orderItem.CartItem.Config),
			},
		}

		// 发送到对应商家的代理程序
		wsHandler := GetWebSocketHandler()
		if err := wsHandler.SendToMerchant(uint64(completeOrder.StoreID), task); err != nil {
			log.Printf("发送打印任务失败 [订单:%d,项目:%d]: %v", order.ID, orderItem.ID, err)
			continue
		}
	}
}
