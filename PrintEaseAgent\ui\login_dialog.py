from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QCheckBox)
from PyQt5.QtCore import Qt
import sys
import logging
from datetime import datetime, timedelta
# 替换导入
from core.database.auth import AuthManager
from utils.device_info import get_device_info
from utils.config_manager import ConfigManager

logger = logging.getLogger(__name__)

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("登录")
        self.setFixedSize(400, 200)
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)

        # 替换数据库管理器
        self.auth_manager = AuthManager()
        self.config_manager = ConfigManager()

        # 创建布局
        layout = QVBoxLayout(self)

        # 用户名输入
        usernameLayout = QHBoxLayout()
        usernameLayout.addWidget(QLabel("账号:"))
        self.usernameEdit = QLineEdit()
        usernameLayout.addWidget(self.usernameEdit)
        layout.addLayout(usernameLayout)

        # 密码输入
        passwordLayout = QHBoxLayout()
        passwordLayout.addWidget(QLabel("密码:"))
        self.passwordEdit = QLineEdit()
        self.passwordEdit.setEchoMode(QLineEdit.Password)
        passwordLayout.addWidget(self.passwordEdit)
        layout.addLayout(passwordLayout)

        # 记住密码选项
        self.rememberCheckBox = QCheckBox("记住密码")
        layout.addWidget(self.rememberCheckBox)

        # 尝试加载上次登录信息
        self._load_last_login()

        # 按钮
        buttonLayout = QHBoxLayout()
        self.loginButton = QPushButton("登录")
        self.loginButton.clicked.connect(self.accept)
        self.cancelButton = QPushButton("取消")
        self.cancelButton.clicked.connect(self.onCancel)
        buttonLayout.addStretch()
        buttonLayout.addWidget(self.loginButton)
        buttonLayout.addWidget(self.cancelButton)
        layout.addStretch()
        layout.addLayout(buttonLayout)

    def _load_last_login(self):
        """加载上次登录信息"""
        try:
            last_auth = self.auth_manager.get_latest_auth()
            if last_auth and last_auth['remember_me']:
                self.usernameEdit.setText(last_auth['merchant_username'])
                self.rememberCheckBox.setChecked(True)
        except Exception as e:
            logger.error(f"加载上次登录信息失败: {str(e)}")

    def save_auth_info(self, login_response):
        """
        保存认证信息到数据库
        :param login_response: 登录接口返回的信息
        """
        try:
            # 获取设备信息
            device_id, device_name = get_device_info()

            # 从响应中获取数据
            if not isinstance(login_response, dict):
                raise ValueError("登录响应格式错误")

            data = login_response.get('data', {})
            if not data:
                raise ValueError("登录响应缺少data字段")

            merchant = data.get('merchant', {})
            if not merchant:
                raise ValueError("登录响应缺少merchant字段")

            token = data.get('token')
            if not token:
                raise ValueError("登录响应缺少token字段")

            logger.info(f"解析登录响应成功: merchant={merchant}, token存在")

            # 保存认证信息
            self.auth_manager.add_auth_record(
                store_id=merchant.get('store_id'),
                store_name=f"Store_{merchant.get('store_id')}",  # 使用默认店铺名
                merchant_id=merchant.get('id'),  # 使用id作为merchant_id
                merchant_username=merchant.get('username'),
                merchant_real_name=merchant.get('real_name'),
                token=token,
                token_expires_at=(datetime.now() + timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S'),
                role_type=merchant.get('merchant_type', 'owner'),  # 使用merchant_type作为role_type
                device_id=device_id,
                device_name=device_name,
                remember_me=self.rememberCheckBox.isChecked()
            )
            logger.info("认证信息保存成功")
        except Exception as e:
            logger.error(f"保存认证信息失败: {str(e)}")
            raise

    def closeEvent(self, event):
        """重写关闭事件，确保点击窗口关闭按钮时退出应用程序"""
        logger.info("用户关闭登录窗口，退出应用程序")
        self.reject()
        event.accept()
        sys.exit(0)

    def onCancel(self):
        """取消按钮点击事件，退出应用程序"""
        logger.info("用户点击取消按钮，退出应用程序")
        self.reject()
        sys.exit(0)

    def getCredentials(self):
        """获取用户输入的账号密码和是否记住密码"""
        return (
            self.usernameEdit.text(),
            self.passwordEdit.text(),
            self.rememberCheckBox.isChecked()
        )
