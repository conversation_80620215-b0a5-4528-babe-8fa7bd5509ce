import json
import logging
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                           QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, 
                           QGroupBox, QTextEdit, QMessageBox, QWidget)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QIcon
from core.database.print_task import PrintTaskManager
from datetime import datetime

logger = logging.getLogger(__name__)

class PrintTaskDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.task_manager = PrintTaskManager()
        self.setWindowTitle("打印任务管理")
        self.setModal(False)  # 非模态对话框
        self.resize(1000, 600)  # 设置合适的窗口大小
        self.initUI()
        
        # 初始化定时器，用于自动刷新任务列表
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.refreshTasks)
        self.timer.start(5000)  # 每5秒刷新一次
        
        # 初始加载任务列表
        self.refreshTasks()
        
    def initUI(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 顶部工具栏
        toolBarLayout = QHBoxLayout()
        
        # 左侧按钮组
        leftButtonsLayout = QHBoxLayout()
        
        refreshBtn = QPushButton("刷新")
        refreshBtn.setIcon(QIcon("static/icons/refresh.png"))
        refreshBtn.clicked.connect(self.refreshTasks)
        leftButtonsLayout.addWidget(refreshBtn)
        
        deleteBtn = QPushButton("删除")
        deleteBtn.setIcon(QIcon("static/icons/delete.png"))
        deleteBtn.clicked.connect(self.deleteTasks)
        leftButtonsLayout.addWidget(deleteBtn)
        
        retryBtn = QPushButton("重试")
        retryBtn.setIcon(QIcon("static/icons/retry.png"))
        retryBtn.clicked.connect(self.retryTasks)
        leftButtonsLayout.addWidget(retryBtn)
        
        cancelBtn = QPushButton("取消")
        cancelBtn.setIcon(QIcon("static/icons/cancel.png"))
        cancelBtn.clicked.connect(self.cancelTasks)
        leftButtonsLayout.addWidget(cancelBtn)
        
        toolBarLayout.addLayout(leftButtonsLayout)
        
        # 右侧过滤器
        filterLayout = QHBoxLayout()
        
        # 状态过滤
        filterLayout.addWidget(QLabel("状态:"))
        self.statusFilter = QComboBox()
        self.statusFilter.addItems(["全部", "等待中", "下载中", "打印中", "已完成", "已取消", "失败"])
        self.statusFilter.currentTextChanged.connect(self.refreshTasks)
        filterLayout.addWidget(self.statusFilter)
        
        toolBarLayout.addLayout(filterLayout)
        toolBarLayout.addStretch()
        
        layout.addLayout(toolBarLayout)
        
        # 分割界面为任务列表和详情
        contentLayout = QHBoxLayout()
        
        # 左侧任务列表
        listGroup = QGroupBox("任务列表")
        listLayout = QVBoxLayout(listGroup)
        
        self.taskTable = QTableWidget(0, 7)
        self.taskTable.setHorizontalHeaderLabels([
            "任务ID", "订单ID", "文件名", "大小", "状态", "进度", "操作"
        ])
        
        # 设置表格属性
        self.taskTable.setSelectionBehavior(QTableWidget.SelectRows)
        self.taskTable.setSelectionMode(QTableWidget.SingleSelection)
        self.taskTable.setEditTriggers(QTableWidget.NoEditTriggers)
        self.taskTable.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.taskTable.verticalHeader().setVisible(False)
        
        # 连接选择信号
        self.taskTable.itemSelectionChanged.connect(self.onTaskSelected)
        
        listLayout.addWidget(self.taskTable)
        contentLayout.addWidget(listGroup, stretch=2)
        
        # 右侧详情区域
        detailGroup = QGroupBox("任务详情")
        detailLayout = QVBoxLayout(detailGroup)
        
        self.detailText = QTextEdit()
        self.detailText.setReadOnly(True)
        detailLayout.addWidget(self.detailText)
        
        contentLayout.addWidget(detailGroup, stretch=1)
        
        layout.addLayout(contentLayout)
        
        # 底部状态栏
        statusLayout = QHBoxLayout()
        self.statusLabel = QLabel("总任务数: 0  等待: 0  打印中: 0  完成: 0  失败: 0")
        statusLayout.addWidget(self.statusLabel)
        layout.addLayout(statusLayout)
    
    def refreshTasks(self):
        """刷新任务列表"""
        try:
            # 保存当前选中的任务ID
            current_task_id = None
            row = self.taskTable.currentRow()
            if row >= 0:
                current_task_id = self.taskTable.item(row, 0).data(Qt.UserRole)
            
            # 获取状态过滤
            status_filter = self.statusFilter.currentText()
            status_map = {
                "等待中": "waiting",
                "下载中": "downloading",
                "打印中": "printing",
                "已完成": "completed",
                "已取消": "cancelled",
                "失败": "failed"
            }
            
            # 获取任务列表
            if status_filter == "全部":
                tasks = self.task_manager.get_all_tasks()
            else:
                tasks = self.task_manager.get_tasks_by_status(status_map[status_filter])
            
            # 清空表格
            self.taskTable.setRowCount(0)
            
            # 统计各状态任务数量
            status_counts = {
                "total": len(tasks),
                "waiting": 0,
                "downloading": 0,
                "printing": 0,
                "completed": 0,
                "cancelled": 0,
                "failed": 0
            }
            
            # 记录要选中的行
            row_to_select = -1
            
            # 填充数据
            for task in tasks:
                row = self.taskTable.rowCount()
                self.taskTable.insertRow(row)
                
                # 更新状态计数
                status_counts[task['status']] += 1
                
                # 设置单元格内容
                self.taskTable.setItem(row, 0, QTableWidgetItem(task['task_id']))
                self.taskTable.setItem(row, 1, QTableWidgetItem(str(task['order_id'])))
                self.taskTable.setItem(row, 2, QTableWidgetItem(task['filename']))
                
                # 格式化文件大小
                size_str = self.format_size(task['file_size'])
                self.taskTable.setItem(row, 3, QTableWidgetItem(size_str))
                
                # 状态显示
                status_display = {
                    "waiting": "等待中",
                    "downloading": "下载中",
                    "printing": "打印中",
                    "completed": "已完成",
                    "cancelled": "已取消",
                    "failed": "失败"
                }
                self.taskTable.setItem(row, 4, QTableWidgetItem(status_display[task['status']]))
                
                # 进度显示
                progress_text = f"{task['progress']}%" if task['progress'] is not None else "-"
                self.taskTable.setItem(row, 5, QTableWidgetItem(progress_text))
                
                # 创建操作按钮
                actionsWidget = QWidget()
                actionsLayout = QHBoxLayout(actionsWidget)
                actionsLayout.setContentsMargins(0, 0, 0, 0)
                
                if task['status'] in ["waiting", "downloading", "printing"]:
                    cancelBtn = QPushButton("取消")
                    cancelBtn.clicked.connect(lambda checked, tid=task['task_id']: self.cancelTask(tid))
                    actionsLayout.addWidget(cancelBtn)
                
                if task['status'] in ["failed"]:
                    retryBtn = QPushButton("重试")
                    retryBtn.clicked.connect(lambda checked, tid=task['task_id']: self.retryTask(tid))
                    actionsLayout.addWidget(retryBtn)
                
                if task['status'] in ["completed", "cancelled", "failed"]:
                    deleteBtn = QPushButton("删除")
                    deleteBtn.clicked.connect(lambda checked, tid=task['task_id']: self.deleteTask(tid))
                    actionsLayout.addWidget(deleteBtn)
                
                self.taskTable.setCellWidget(row, 6, actionsWidget)
                
                # 存储任务ID
                self.taskTable.item(row, 0).setData(Qt.UserRole, task['task_id'])
                
                # 如果是之前选中的任务，记录行号
                if task['task_id'] == current_task_id:
                    row_to_select = row
            
            # 更新状态栏
            self.statusLabel.setText(
                f"总任务数: {status_counts['total']}  "
                f"等待: {status_counts['waiting']}  "
                f"下载中: {status_counts['downloading']}  "
                f"打印中: {status_counts['printing']}  "
                f"完成: {status_counts['completed']}  "
                f"取消: {status_counts['cancelled']}  "
                f"失败: {status_counts['failed']}"
            )
            
            # 恢复选中状态
            if row_to_select >= 0:
                self.taskTable.selectRow(row_to_select)
            
        except Exception as e:
            logger.error(f"刷新任务列表失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"刷新任务列表失败: {str(e)}")
    
    def onTaskSelected(self):
        """处理任务选择事件"""
        selected_items = self.taskTable.selectedItems()
        if not selected_items:
            self.detailText.clear()
            return
        
        # 获取选中的任务ID
        row = self.taskTable.currentRow()
        task_id = self.taskTable.item(row, 0).data(Qt.UserRole)
        
        # 从数据库获取任务详情
        task = self.task_manager.get_task(task_id)
        if not task:
            return
        
        # 更新详情文本
        detail_text = f"""
任务信息：
  任务ID：{task['task_id']}
  订单ID：{task['order_id']}
  订单项ID：{task['order_item_id']}

文件信息：
  文件ID：{task['file_id']}
  文件名：{task['filename']}
  文件类型：{task['file_type']}
  文件大小：{self.format_size(task['file_size'])}
  页数：{task['page_count'] or '未知'}
  本地路径：{task['local_path'] or '未下载'}

打印配置：
{self.format_print_config(task['print_config'])}

任务状态：
  当前状态：{task['status']}
  进度：{f"{task['progress']}%" if task['progress'] is not None else "未开始"}
  重试次数：{task['retry_count']}
  错误信息：{task['error_message'] or '无'}

时间信息：
  创建时间：{task['created_at']}
  开始时间：{task['started_at'] or '未开始'}
  完成时间：{task['completed_at'] or '未完成'}
  更新时间：{task['updated_at']}
"""
        self.detailText.setText(detail_text)
    
    def format_size(self, size_in_bytes):
        """格式化文件大小显示"""
        if size_in_bytes is None:
            return "未知"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_in_bytes < 1024:
                return f"{size_in_bytes:.1f} {unit}"
            size_in_bytes /= 1024
        return f"{size_in_bytes:.1f} TB"
    
    def format_print_config(self, config_str):
        """格式化打印配置显示"""
        if not config_str:
            return "  无配置信息"
        
        try:
            config = json.loads(config_str)
            
            # 定义配置项的显示顺序和中文映射
            config_order = [
                ('paperSize', '纸型'),
                ('colorMode', '颜色'),
                ('sideMode', '单双面'),
                ('copies', '份数'),
                ('pagesPerSheet', '多页合一'),
                ('page_range', '文档打印范围')  # 新增合并的页面范围配置项
            ]
            
            # 值的中文映射
            value_map = {
                # 纸型
                'A4': 'A4',
                'A3': 'A3',
                # 颜色
                'black': '黑白',
                'color': '彩色',
                # 单双面
                'single': '单面',
                'double': '双面'
            }
            
            result = []
            for key, label in config_order:
                if key == 'page_range':
                    # 特殊处理页面范围显示
                    if 'startPage' in config and 'endPage' in config:
                        start_page = config['startPage']
                        end_page = config['endPage']
                        total_pages = config.get('totalPages', end_page)  # 如果没有总页数，使用结束页作为总页数
                        result.append(f"  {label}：第{start_page}-{end_page}页（共{total_pages}页）")
                elif key in config:
                    value = config[key]
                    # 使用映射值（如果存在），否则使用原值
                    display_value = value_map.get(str(value), str(value))
                    
                    # 特殊处理多页合一的显示
                    if key == 'pagesPerSheet':
                        display_value = f'每版打印{value}页'
                    
                    result.append(f"  {label}: {display_value}")
            
            return "\n".join(result)
        except:
            return "  配置解析失败"
    
    def deleteTask(self, task_id):
        """删除单个任务"""
        try:
            task = self.task_manager.get_task(task_id)
            if not task:
                raise Exception("任务不存在")
            
            if task['status'] not in ["completed", "cancelled", "failed"]:
                raise Exception("只能删除已完成、已取消或失败的任务")
            
            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除任务 {task_id} 吗？\n此操作不可恢复。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.task_manager.delete_task(task_id)
                self.refreshTasks()
                QMessageBox.information(self, "成功", "任务已删除")
                
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除任务失败: {str(e)}")
    
    def deleteTasks(self):
        """批量删除任务"""
        try:
            # 获取选中的任务
            selected_rows = set(item.row() for item in self.taskTable.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要删除的任务")
                return
            
            # 获取任务ID列表
            task_ids = []
            for row in selected_rows:
                task_id = self.taskTable.item(row, 0).data(Qt.UserRole)
                task = self.task_manager.get_task(task_id)
                if task and task['status'] in ["completed", "cancelled", "failed"]:
                    task_ids.append(task_id)
            
            if not task_ids:
                QMessageBox.warning(self, "警告", "没有可删除的任务\n只能删除已完成、已取消或失败的任务")
                return
            
            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {len(task_ids)} 个任务吗？\n此操作不可恢复。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                for task_id in task_ids:
                    self.task_manager.delete_task(task_id)
                self.refreshTasks()
                QMessageBox.information(self, "成功", f"已删除 {len(task_ids)} 个任务")
                
        except Exception as e:
            logger.error(f"批量删除任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"批量删除任务失败: {str(e)}")
    
    def retryTask(self, task_id):
        """重试单个任务"""
        try:
            task = self.task_manager.get_task(task_id)
            if not task:
                raise Exception("任务不存在")
            
            if task['status'] != "failed":
                raise Exception("只能重试失败的任务")
            
            # 更新任务状态
            self.task_manager.update_task_status(task_id, "waiting")
            self.task_manager.increment_retry_count(task_id)
            
            self.refreshTasks()
            QMessageBox.information(self, "成功", "任务已重新加入队列")
            
        except Exception as e:
            logger.error(f"重试任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"重试任务失败: {str(e)}")
    
    def retryTasks(self):
        """批量重试任务"""
        try:
            # 获取选中的任务
            selected_rows = set(item.row() for item in self.taskTable.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要重试的任务")
                return
            
            # 获取任务ID列表
            task_ids = []
            for row in selected_rows:
                task_id = self.taskTable.item(row, 0).data(Qt.UserRole)
                task = self.task_manager.get_task(task_id)
                if task and task['status'] == "failed":
                    task_ids.append(task_id)
            
            if not task_ids:
                QMessageBox.warning(self, "警告", "没有可重试的任务\n只能重试失败的任务")
                return
            
            # 确认重试
            reply = QMessageBox.question(
                self,
                "确认重试",
                f"确定要重试选中的 {len(task_ids)} 个任务吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                for task_id in task_ids:
                    task = self.task_manager.get_task(task_id)
                    self.task_manager.update_task_status(task_id, "waiting")
                    self.task_manager.increment_retry_count(task_id)
                
                self.refreshTasks()
                QMessageBox.information(self, "成功", f"已重新加入 {len(task_ids)} 个任务到队列")
                
        except Exception as e:
            logger.error(f"批量重试任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"批量重试任务失败: {str(e)}")
    
    def cancelTask(self, task_id):
        """取消单个任务"""
        try:
            task = self.task_manager.get_task(task_id)
            if not task:
                raise Exception("任务不存在")
            
            if task['status'] not in ["waiting", "downloading", "printing"]:
                raise Exception("只能取消等待中、下载中或打印中的任务")
            
            # 确认取消
            reply = QMessageBox.question(
                self,
                "确认取消",
                f"确定要取消任务 {task_id} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.task_manager.update_task_status(task_id, "cancelled")
                self.refreshTasks()
                QMessageBox.information(self, "成功", "任务已取消")
                
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"取消任务失败: {str(e)}")
    
    def cancelTasks(self):
        """批量取消任务"""
        try:
            # 获取选中的任务
            selected_rows = set(item.row() for item in self.taskTable.selectedItems())
            if not selected_rows:
                QMessageBox.warning(self, "警告", "请先选择要取消的任务")
                return
            
            # 获取任务ID列表
            task_ids = []
            for row in selected_rows:
                task_id = self.taskTable.item(row, 0).data(Qt.UserRole)
                task = self.task_manager.get_task(task_id)
                if task and task['status'] in ["waiting", "downloading", "printing"]:
                    task_ids.append(task_id)
            
            if not task_ids:
                QMessageBox.warning(self, "警告", "没有可取消的任务\n只能取消等待中、下载中或打印中的任务")
                return
            
            # 确认取消
            reply = QMessageBox.question(
                self,
                "确认取消",
                f"确定要取消选中的 {len(task_ids)} 个任务吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                for task_id in task_ids:
                    self.task_manager.update_task_status(task_id, "cancelled")
                
                self.refreshTasks()
                QMessageBox.information(self, "成功", f"已取消 {len(task_ids)} 个任务")
                
        except Exception as e:
            logger.error(f"批量取消任务失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"批量取消任务失败: {str(e)}")
