package handlers

import (
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"fmt"
	"net/http"
	"os"
	"strconv"

	"github.com/gin-gonic/gin"
)

// DownloadHandler 处理文件下载
func DownloadHandler(c *gin.Context) {
	// 获取文件ID
	fileID, err := strconv.ParseUint(c.Query("file_id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的文件ID",
		})
		return
	}

	// 查询文件记录
	var file models.File
	if err := database.DB.First(&file, fileID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code": -1,
			"msg":  "文件不存在",
		})
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(file.FilePath); os.IsNotExist(err) {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"code": -1,
			"msg":  "文件不存在",
		})
		return
	}

	// 获取文件类型
	contentType := getContentType(file.FileType)

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Type", contentType)

	// 使用 inline 来支持预览
	c.Header("Content-Disposition", fmt.Sprintf("inline; filename=%s", file.Filename))

	// 发送文件
	c.File(file.FilePath)
}

// getContentType 根据文件类型返回Content-Type
func getContentType(fileType string) string {
	switch fileType {
	case "pdf":
		return "application/pdf"
	case "doc", "docx":
		return "application/msword"
	case "jpg", "jpeg":
		return "image/jpeg"
	case "png":
		return "image/png"
	case "txt":
		return "text/plain"
	default:
		return "application/octet-stream"
	}
}
