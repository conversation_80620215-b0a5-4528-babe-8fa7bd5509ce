import sqlite3
import os
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from .config_manager import ConfigManager

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.config_manager = ConfigManager()
        self.db_path = self.config_manager.get_database_path()
        # 确保数据目录存在
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        self.connection: Optional[sqlite3.Connection] = None
        self._init_db()
    
    def _init_db(self):
        """初始化数据库"""
        try:
            self.connect()
            self._create_tables()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise
        finally:
            self.close()
    
    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def _create_tables(self):
        """创建数据库表"""
        if not self.connection:
            raise Exception("数据库未连接")
        
        # 创建auth表
        self.connection.execute("""
        CREATE TABLE IF NOT EXISTS auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_id INTEGER NOT NULL,            -- 店铺ID
            store_name TEXT NOT NULL,             -- 店铺名称
            merchant_id INTEGER NOT NULL,         -- 商户ID
            merchant_username TEXT NOT NULL,      -- 商户用户名
            merchant_real_name TEXT NOT NULL,     -- 商户真实姓名
            token TEXT NOT NULL,                  -- JWT token
            token_expires_at TIMESTAMP NOT NULL,  -- token过期时间
            role_type TEXT NOT NULL,              -- 角色类型
            device_id TEXT NOT NULL,              -- 设备唯一标识
            device_name TEXT NOT NULL,            -- 设备名称
            remember_me BOOLEAN DEFAULT 0,        -- 是否记住登录
            login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 登录时间
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- token更新时间
        )
        """)

        # 创建打印机表
        self.connection.execute("""
        CREATE TABLE IF NOT EXISTS printers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            -- 打印机基本信息
            name VARCHAR(100) NOT NULL,                -- 打印机名称
            printer_id VARCHAR(50) NOT NULL,           -- 打印机唯一标识（与后台对应）
            system_name VARCHAR(100) NOT NULL,         -- 系统中的打印机名称
            
            -- 打印机状态
            status INTEGER NOT NULL DEFAULT 0,         -- 打印机状态：0-离线 1-在线 2-打印中 3-错误
            error_message TEXT,                        -- 错误信息
            paper_status INTEGER DEFAULT 1,            -- 纸张状态：0-缺纸 1-正常
            
            -- 打印机配置
            config TEXT,                              -- 打印机配置（JSON格式）
                                                      -- 配置JSON格式示例：
                                                      -- {
                                                      --     "default_settings": {
                                                      --         "color_mode": "black",     -- 默认打印模式：black/color
                                                      --         "duplex": true,           -- 默认双面打印
                                                      --         "copies": 1,              -- 默认打印份数
                                                      --         "paper_size": "A4",       -- 默认纸张大小
                                                      --         "orientation": "portrait", -- 默认方向：portrait/landscape
                                                      --         "quality": "normal"       -- 默认打印质量：draft/normal/high
                                                      --     },
                                                      --     "supported_paper": [          -- 支持的纸张类型列表
                                                      --         {
                                                      --             "name": "A4",         -- 纸张名称
                                                      --             "width": 210,         -- 宽度（毫米）
                                                      --             "height": 297,        -- 高度（毫米）
                                                      --             "default": true       -- 是否默认
                                                      --         },
                                                      --         {
                                                      --             "name": "A3",
                                                      --             "width": 297,
                                                      --             "height": 420,
                                                      --             "default": false
                                                      --         }
                                                      --     ],
                                                      --     "supported_features": {       -- 打印机支持的功能
                                                      --         "color": true,           -- 是否支持彩色打印
                                                      --         "duplex": true,          -- 是否支持双面打印
                                                      --         "paper_sizes": [         -- 支持的纸张尺寸列表
                                                      --             "A3", "A4", "B4", "B5"
                                                      --         ],
                                                      --         "quality_options": [      -- 支持的打印质量选项
                                                      --             "draft",             -- 草稿质量
                                                      --             "normal",            -- 正常质量
                                                      --             "high"               -- 高质量
                                                      --         ]
                                                      --     },
                                                      --     "printer_info": {            -- 打印机硬件信息
                                                      --         "manufacturer": "HP",     -- 制造商
                                                      --         "model": "LaserJet Pro",  -- 型号
                                                      --         "driver_name": "HP LaserJet Pro Driver",  -- 驱动名称
                                                      --         "driver_version": "1.0.0" -- 驱动版本
                                                      --     }
                                                      -- }
            
            -- 同步相关
            backend_id INTEGER,                       -- 后台系统中的打印机ID
            last_sync TIMESTAMP,                      -- 最后同步时间
            sync_status INTEGER DEFAULT 0,            -- 同步状态：0-未同步 1-已同步 2-同步失败
            
            -- 系统信息
            os_type VARCHAR(20) NOT NULL,             -- 操作系统类型：windows/linux/macos
            driver_name TEXT,                         -- 打印机驱动名称
            port_name TEXT,                           -- 打印机端口名称
            
            -- 时间信息
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            -- 唯一索引
            UNIQUE(printer_id),
            UNIQUE(system_name, os_type)
        )
        """)

        # 创建打印机状态更新触发器
        self.connection.execute("""
        CREATE TRIGGER IF NOT EXISTS update_printers_timestamp 
        AFTER UPDATE ON printers
        FOR EACH ROW
        BEGIN
            UPDATE printers SET updated_at = CURRENT_TIMESTAMP
            WHERE id = old.id;
        END;
        """)
        
        self.connection.commit()
    
    def add_auth_record(self, store_id: int, store_name: str,
                       merchant_id: int, merchant_username: str, merchant_real_name: str,
                       token: str, token_expires_at: str, role_type: str,
                       device_id: str, device_name: str, remember_me: bool = False) -> int:
        """添加新的认证记录"""
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO auth (
                    store_id, store_name, merchant_id, merchant_username, merchant_real_name,
                    token, token_expires_at, role_type, device_id, device_name, remember_me
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (store_id, store_name, merchant_id, merchant_username, merchant_real_name,
                  token, token_expires_at, role_type, device_id, device_name, remember_me))
            self.connection.commit()
            return cursor.lastrowid
        finally:
            self.close()
    
    def update_token(self, auth_id: int, new_token: str, new_expires_at: str):
        """更新token信息"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE auth 
                SET token = ?, token_expires_at = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (new_token, new_expires_at, auth_id))
            self.connection.commit()
        finally:
            self.close()
    
    def get_latest_auth(self) -> Optional[Dict[str, Any]]:
        """获取最新的认证记录"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM auth 
                ORDER BY id DESC LIMIT 1
            """)
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()
    
    def get_auth_history(self) -> List[Dict[str, Any]]:
        """获取认证历史记录"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM auth 
                ORDER BY login_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def add_printer(self, name: str, printer_id: str, system_name: str, os_type: str,
                   driver_name: str = None, port_name: str = None, config: str = None) -> int:
        """添加新的打印机记录"""
        try:
            self.connect()
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO printers (
                    name, printer_id, system_name, os_type, 
                    driver_name, port_name, config
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (name, printer_id, system_name, os_type, 
                 driver_name, port_name, config))
            self.connection.commit()
            return cursor.lastrowid
        finally:
            self.close()

    def update_printer_status(self, printer_id: str, status: int, 
                            error_message: str = None, paper_status: int = None):
        """更新打印机状态"""
        try:
            self.connect()
            update_fields = ["status = ?"]
            params = [status]
            
            if error_message is not None:
                update_fields.append("error_message = ?")
                params.append(error_message)
            
            if paper_status is not None:
                update_fields.append("paper_status = ?")
                params.append(paper_status)
            
            params.append(printer_id)
            
            sql = f"""
                UPDATE printers 
                SET {', '.join(update_fields)}
                WHERE printer_id = ?
            """
            self.connection.execute(sql, params)
            self.connection.commit()
        finally:
            self.close()

    def update_printer_sync_status(self, printer_id: str, backend_id: int, 
                                 sync_status: int = 1):
        """更新打印机同步状态"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers 
                SET backend_id = ?, sync_status = ?, last_sync = CURRENT_TIMESTAMP
                WHERE printer_id = ?
            """, (backend_id, sync_status, printer_id))
            self.connection.commit()
        finally:
            self.close()

    def update_printer_config(self, printer_id: str, config: str):
        """更新打印机配置"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers 
                SET config = ?
                WHERE printer_id = ?
            """, (config, printer_id))
            self.connection.commit()
        finally:
            self.close()

    def get_printer_by_id(self, printer_id: str) -> Optional[Dict[str, Any]]:
        """根据打印机ID获取打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers 
                WHERE printer_id = ?
            """, (printer_id,))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_printer_by_system_name(self, system_name: str, 
                                 os_type: str) -> Optional[Dict[str, Any]]:
        """根据系统打印机名称获取打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers 
                WHERE system_name = ? AND os_type = ?
            """, (system_name, os_type))
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_all_printers(self) -> List[Dict[str, Any]]:
        """获取所有打印机信息"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers 
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def get_online_printers(self) -> List[Dict[str, Any]]:
        """获取所有在线的打印机"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM printers 
                WHERE status = 1
                ORDER BY created_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()

    def delete_printer(self, printer_id: str):
        """删除打印机记录"""
        try:
            self.connect()
            self.connection.execute("""
                DELETE FROM printers 
                WHERE printer_id = ?
            """, (printer_id,))
            self.connection.commit()
        finally:
            self.close()

    def update_printer_name(self, printer_id: str, name: str):
        """更新打印机名称"""
        try:
            self.connect()
            self.connection.execute("""
                UPDATE printers 
                SET name = ?
                WHERE printer_id = ?
            """, (name, printer_id))
            self.connection.commit()
        finally:
            self.close() 