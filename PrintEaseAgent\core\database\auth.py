from typing import Optional, Dict, Any, List
from datetime import datetime
from .base import BaseManager

class AuthManager(BaseManager):
    """认证数据库管理类"""

    def _create_tables(self):
        """创建认证相关表"""
        if not self.connection:
            raise Exception("数据库未连接")

        # 创建auth表
        self.connection.execute("""
        CREATE TABLE IF NOT EXISTS auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            store_id INTEGER NOT NULL,            -- 店铺ID
            store_name TEXT NOT NULL,             -- 店铺名称
            merchant_id INTEGER NOT NULL,         -- 商户ID
            merchant_username TEXT NOT NULL,      -- 商户用户名
            merchant_real_name TEXT NOT NULL,     -- 商户真实姓名
            token TEXT NOT NULL,                  -- JWT token
            token_expires_at TIMESTAMP NOT NULL,  -- token过期时间
            role_type TEXT NOT NULL,              -- 角色类型
            device_id TEXT NOT NULL,              -- 设备唯一标识
            device_name TEXT NOT NULL,            -- 设备名称
            remember_me BOOLEAN DEFAULT 0,        -- 是否记住登录
            login_at TIMESTAMP DEFAULT (datetime('now', 'localtime')),  -- 登录时间
            updated_at TIMESTAMP DEFAULT (datetime('now', 'localtime')) -- token更新时间
        )
        """)

        # 检查并删除已存在的触发器
        self.connection.execute("""
        DROP TRIGGER IF EXISTS update_auth_timestamp;
        """)

        # 创建auth表更新触发器
        self.connection.execute("""
        CREATE TRIGGER update_auth_timestamp
        AFTER UPDATE ON auth
        FOR EACH ROW
        BEGIN
            UPDATE auth SET updated_at = datetime('now', 'localtime')
            WHERE id = old.id;
        END;
        """)

        self.connection.commit()

    def add_auth_record(self, store_id: int, store_name: str,
                       merchant_id: int, merchant_username: str, merchant_real_name: str,
                       token: str, token_expires_at: str, role_type: str,
                       device_id: str, device_name: str, remember_me: bool = False) -> int:
        """添加新的认证记录"""
        try:
            # 转换 token_expires_at 格式
            # 如果是 ISO 格式 (2025-05-17T00:21:40.274007)，转换为 SQLite 格式 (2025-05-17 00:21:40)
            if 'T' in token_expires_at:
                try:
                    dt = datetime.fromisoformat(token_expires_at.split('.')[0] if '.' in token_expires_at else token_expires_at)
                    token_expires_at = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    # 如果转换失败，保留原格式
                    pass

            self.connect()
            cursor = self.connection.cursor()
            cursor.execute("""
                INSERT INTO auth (
                    store_id, store_name, merchant_id, merchant_username, merchant_real_name,
                    token, token_expires_at, role_type, device_id, device_name, remember_me,
                    login_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
            """, (store_id, store_name, merchant_id, merchant_username, merchant_real_name,
                  token, token_expires_at, role_type, device_id, device_name, remember_me))
            self.connection.commit()
            return cursor.lastrowid
        finally:
            self.close()

    def update_token(self, auth_id: int, new_token: str, new_expires_at: str):
        """更新token信息"""
        try:
            # 转换 new_expires_at 格式
            # 如果是 ISO 格式 (2025-05-17T00:21:40.274007)，转换为 SQLite 格式 (2025-05-17 00:21:40)
            if 'T' in new_expires_at:
                try:
                    dt = datetime.fromisoformat(new_expires_at.split('.')[0] if '.' in new_expires_at else new_expires_at)
                    new_expires_at = dt.strftime('%Y-%m-%d %H:%M:%S')
                except Exception as e:
                    # 如果转换失败，保留原格式
                    pass

            self.connect()
            self.connection.execute("""
                UPDATE auth
                SET token = ?, token_expires_at = ?, updated_at = datetime('now', 'localtime')
                WHERE id = ?
            """, (new_token, new_expires_at, auth_id))
            self.connection.commit()
        finally:
            self.close()

    def get_latest_auth(self) -> Optional[Dict[str, Any]]:
        """获取最新的认证记录"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM auth
                ORDER BY id DESC LIMIT 1
            """)
            row = cursor.fetchone()
            return dict(row) if row else None
        finally:
            self.close()

    def get_auth_history(self) -> List[Dict[str, Any]]:
        """获取认证历史记录"""
        try:
            self.connect()
            cursor = self.connection.execute("""
                SELECT * FROM auth
                ORDER BY login_at DESC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            self.close()