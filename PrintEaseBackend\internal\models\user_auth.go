package models

import "time"

// UserAuth 用户授权表
type UserAuth struct {
	ID           int64     `gorm:"primarykey" json:"id"`
	UserID       int64     `gorm:"not null;index" json:"user_id"`         // 关联的用户ID
	IdentityType string    `gorm:"size:20;not null" json:"identity_type"` // 登录类型：wx_mini/qq_mini/web/client
	Identifier   string    `gorm:"size:100;not null" json:"identifier"`   // 标识（openid/用户名/手机号/邮箱）
	Credential   string    `gorm:"size:255" json:"-"`                     // 密码凭证
	Verified     bool      `gorm:"default:false" json:"verified"`         // 是否验证
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`

	User User `gorm:"foreignKey:UserID" json:"user"` // 关联用户模型
}

func (UserAuth) TableName() string {
	return "user_auths"
}

// Indexes gorm 索引设置
func (UserAuth) Indexes() [][]string {
	return [][]string{
		{"identity_type", "identifier"},
	}
}
