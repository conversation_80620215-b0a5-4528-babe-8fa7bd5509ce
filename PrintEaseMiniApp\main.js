import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import * as utils from './utils/util'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
export function createApp() {
	const app = createSSRApp(App)
	
	// 注册store
	app.use(store)
	
	// 挂载全局工具函数
	app.config.globalProperties.$utils = utils
	
	return {
		app
	}
}
// #endif