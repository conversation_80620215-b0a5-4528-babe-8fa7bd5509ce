package services

import (
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"fmt"
)

// StoreService 商家服务
type StoreService struct{}

// GetStoreByID 根据ID获取商家信息
func (s *StoreService) GetStoreByID(id uint64) (*models.Store, error) {
	var store models.Store
	result := database.DB.First(&store, id)
	if result.Error != nil {
		return nil, result.Error
	}
	return &store, nil
}

// CreateStore 创建商家
func (s *StoreService) CreateStore(store *models.Store) error {
	return database.DB.Create(store).Error
}

// UpdateStore 更新商家信息
func (s *StoreService) UpdateStore(store *models.Store) error {
	return database.DB.Save(store).Error
}

// DeleteStore 删除商家（软删除）
func (s *StoreService) DeleteStore(id uint64) error {
	return database.DB.Delete(&models.Store{}, id).Error
}

// GetStoreList 获取所有营业中的店铺列表
func (s *StoreService) GetStoreList(stores *[]models.Store) error {
	return database.DB.
		Order("created_at desc").
		Find(stores).Error
}

// GetNearbyStores 获取附近的店铺
func (s *StoreService) GetNearbyStores(latitude, longitude float64, maxDistance float64) ([]models.Store, error) {
	var stores []models.Store

	// 修改 SQL 查询，给距离字段一个明确的别名
	query := `
		SELECT 
			stores.*,  -- 先选择所有店铺字段
			ST_Distance_Sphere(
				point(longitude, latitude),
				point(?, ?)
			) / 1000 as calculated_distance  -- 给计算的距离一个明确的别名
		FROM stores
		HAVING calculated_distance <= ?
		ORDER BY calculated_distance
	`

	// 创建一个临时结构体来接收查询结果
	type StoreWithDistance struct {
		models.Store
		CalculatedDistance float64 `gorm:"column:calculated_distance"`
	}

	var results []StoreWithDistance

	err := database.DB.Raw(query, longitude, latitude, maxDistance).Scan(&results).Error
	if err != nil {
		return nil, fmt.Errorf("获取附近店铺失败: %v", err)
	}

	// 将结果转换为 Store 切片，同时设置 Distance 字段
	stores = make([]models.Store, len(results))
	for i, result := range results {
		stores[i] = result.Store
		stores[i].Distance = result.CalculatedDistance
		// 转换status状态
		switch stores[i].Status {
		case "0":
			stores[i].Status = "open"
		case "1":
			stores[i].Status = "closed"
		case "2":
			stores[i].Status = "likely_shutdown"
		case "3":
			stores[i].Status = "shutdown"
		}
	}

	return stores, nil
}
