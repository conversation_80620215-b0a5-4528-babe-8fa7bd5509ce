// 选择文件
async chooseFile() {
    try {
        const res = await uni.chooseMessageFile({
            count: 1,
            type: 'file',
            extension: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']
        });

        if (res.tempFiles && res.tempFiles.length > 0) {
            const file = res.tempFiles[0];
            
            // 详细打印文件信息
            console.log('选择的文件详情:', {
                name: file.name,           // 文件名
                path: file.path,           // 文件路径
                size: file.size,           // 文件大小
                type: file.type,           // 文件类型
                lastModified: file.lastModified, // 最后修改时间（如果有）
                tempFilePath: file.tempFilePath  // 临时文件路径（如果有）
            });

            // 上传文件
            const uploadRes = await uni.uploadFile({
                url: BASE_URL + '/api/upload',
                filePath: file.path,
                name: 'file',
                formData: {
                    filename: file.name  // 使用file.name作为文件名
                }
            });
        }
    } catch (error) {
        console.error('选择文件失败:', error);
        uni.showToast({
            title: '选择文件失败',
            icon: 'none'
        });
    }
}