import sys
import logging
from PyQt5.QtWidgets import (Q<PERSON><PERSON><PERSON>, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                            QHBoxLayout, QPushButton, QLabel, QTableWidget, QTableWidgetItem,
                            QHeaderView, QComboBox, QStatusBar, QToolBar, QAction, QMessageBox,
                            QFrame, QTextEdit, QGroupBox, QDialog, QLineEdit)
from PyQt5.QtCore import QTimer, QSize
from PyQt5.QtGui import QIcon, QFont
from ui.login_dialog import LoginDialog
from core.api_client import ApiClient
from core.websocket_client import WebSocketClient
from core.message_handler import MessageHandler
from core.print_manager import PrintManager
from core.print_task_processor import PrintTaskProcessor
from utils.config_manager import ConfigManager
from utils.file_downloader import FileDownloader
from utils.log_utils import setup_logging, LogCapture
from datetime import datetime
from core.database import <PERSON><PERSON><PERSON><PERSON><PERSON>, AuthManager
from utils.device_info import get_device_info
import jwt
import json
from PyQt5.QtCore import Qt
from .printer_dialogs import AddPrinterDialog, EditPrinterDialog, ConfigurePrinterDialog
from .print_task_dialog import PrintTaskDialog

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # 设置日志
        setup_logging()
        self.log_capture = LogCapture()
        self.log_capture.setup()

        # 初始化配置和管理器
        self.config_manager = ConfigManager()
        self.printer_manager_db = PrinterManager()
        self.auth_manager_db = AuthManager()
        self.api_client = ApiClient(self.config_manager.get_api_url())

        # 初始化文件下载器
        self.file_downloader = FileDownloader(self.api_client)

        # 初始化打印管理器
        self.print_manager = PrintManager()

        # 初始化打印任务处理器
        uploads_dir = self.config_manager.get_uploads_dir()
        self.task_processor = PrintTaskProcessor(uploads_dir)

        # 启动打印任务处理线程
        self.task_processor.start()

        # 初始化消息处理器
        self.message_handler = MessageHandler(self.file_downloader, uploads_dir, self.task_processor)

        # 处理所有待处理的任务
        self.task_processor.process_pending_tasks()

        # WebSocket客户端
        self.ws_client = None

        # 获取设备信息
        self.device_id, self.device_name = get_device_info()

        self.initUI()

        # 验证登录状态
        self.check_auth_status()

    def initUI(self):
        # 设置窗口基本属性
        self.setWindowTitle("打印代理程序")
        self.setGeometry(100, 100, 1000, 700)

        # 创建工具栏
        self.createToolBar()

        # 创建中央部件
        self.centralWidget = QWidget()
        self.setCentralWidget(self.centralWidget)

        # 创建主布局
        mainLayout = QVBoxLayout(self.centralWidget)

        # 创建标签页控件
        self.tabWidget = QTabWidget()
        mainLayout.addWidget(self.tabWidget)

        # 添加各个标签页
        self.createDashboardTab()
        self.createPrintersTab()
        self.createJobsTab()
        self.createSettingsTab()
        self.createLogsTab()

        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)

        # 添加状态信息
        self.statusLabel = QLabel("就绪")
        self.statusBar.addWidget(self.statusLabel, 1)

        # 添加云连接状态
        self.cloudStatusLabel = QLabel("云连接状态: 未连接")
        self.statusBar.addPermanentWidget(self.cloudStatusLabel)

        # 初始化定时器，用于更新状态
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.updateStatus)
        self.timer.start(5000)  # 每5秒更新一次状态

        # 显示初始状态
        self.updateStatus()

        # 连接消息处理器信号
        self.connectMessageHandlerSignals()

        # 连接文件下载器信号
        self.connectFileDownloaderSignals()

    def createToolBar(self):
        # 创建工具栏
        self.toolBar = QToolBar("主工具栏")
        self.toolBar.setIconSize(QSize(32, 32))
        self.addToolBar(self.toolBar)

        # 添加刷新按钮
        refreshAction = QAction(QIcon("static/icons/refresh.png"), "刷新", self)
        refreshAction.triggered.connect(self.refreshAll)
        self.toolBar.addAction(refreshAction)

        self.toolBar.addSeparator()

        # 添加连接云端按钮
        connectAction = QAction(QIcon("static/icons/cloud.png"), "连接云端", self)
        connectAction.triggered.connect(self.connectToCloud)
        self.toolBar.addAction(connectAction)

        # 添加断开连接按钮
        disconnectAction = QAction(QIcon("static/icons/disconnect.png"), "断开连接", self)
        disconnectAction.triggered.connect(self.disconnectFromCloud)
        self.toolBar.addAction(disconnectAction)

        self.toolBar.addSeparator()

        # 添加帮助按钮
        helpAction = QAction(QIcon("static/icons/help.png"), "帮助", self)
        helpAction.triggered.connect(self.showHelp)
        self.toolBar.addAction(helpAction)

        # 添加登出按钮
        self.toolBar.addSeparator()
        logoutAction = QAction(QIcon("static/icons/logout.png"), "登出", self)
        logoutAction.triggered.connect(self.logout)
        self.toolBar.addAction(logoutAction)

        # 添加重新登录按钮
        reloginAction = QAction(QIcon("static/icons/login.png"), "重新登录", self)
        reloginAction.triggered.connect(self.showLoginDialog)
        self.toolBar.addAction(reloginAction)

    def createDashboardTab(self):
        # 创建仪表盘标签页
        dashboardTab = QWidget()
        self.tabWidget.addTab(dashboardTab, "仪表盘")

        # 创建布局
        layout = QVBoxLayout(dashboardTab)

        # 添加状态概览组
        statusGroup = QGroupBox("系统状态")
        statusLayout = QHBoxLayout(statusGroup)

        # 打印机状态
        printerStatusFrame = QFrame()
        printerStatusFrame.setFrameShape(QFrame.StyledPanel)
        printerStatusLayout = QVBoxLayout(printerStatusFrame)
        printerStatusLayout.addWidget(QLabel("打印机状态"))
        self.printerStatusLabel = QLabel("在线: 0 | 离线: 0 | 错误: 0")
        printerStatusLayout.addWidget(self.printerStatusLabel)
        statusLayout.addWidget(printerStatusFrame)

        # 任务状态
        jobStatusFrame = QFrame()
        jobStatusFrame.setFrameShape(QFrame.StyledPanel)
        jobStatusLayout = QVBoxLayout(jobStatusFrame)
        jobStatusLayout.addWidget(QLabel("任务状态"))
        self.jobStatusLabel = QLabel("等待: 0 | 打印中: 0 | 完成: 0 | 失败: 0")
        jobStatusLayout.addWidget(self.jobStatusLabel)
        statusLayout.addWidget(jobStatusFrame)

        # 云连接状态
        cloudStatusFrame = QFrame()
        cloudStatusFrame.setFrameShape(QFrame.StyledPanel)
        cloudStatusLayout = QVBoxLayout(cloudStatusFrame)
        cloudStatusLayout.addWidget(QLabel("云连接状态"))
        self.cloudDetailStatusLabel = QLabel("未连接")
        cloudStatusLayout.addWidget(self.cloudDetailStatusLabel)
        statusLayout.addWidget(cloudStatusFrame)

        layout.addWidget(statusGroup)

        # 添加最近任务组
        recentJobsGroup = QGroupBox("最近任务")
        recentJobsLayout = QVBoxLayout(recentJobsGroup)

        self.recentJobsTable = QTableWidget(0, 5)
        self.recentJobsTable.setHorizontalHeaderLabels(["任务ID", "文件名", "打印机", "状态", "时间"])
        self.recentJobsTable.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        recentJobsLayout.addWidget(self.recentJobsTable)

        layout.addWidget(recentJobsGroup)

        # 添加系统信息组
        sysInfoGroup = QGroupBox("系统信息")
        sysInfoLayout = QVBoxLayout(sysInfoGroup)

        self.sysInfoText = QTextEdit()
        self.sysInfoText.setReadOnly(True)
        self.sysInfoText.setText("系统版本: PrintEase Agent v1.0\n"
                                "Python版本: 3.10.11\n"
                                "操作系统: Windows 10\n"
                                "启动时间: 2023-03-08 12:00:00")
        sysInfoLayout.addWidget(self.sysInfoText)

        layout.addWidget(sysInfoGroup)

    def createPrintersTab(self):
        # 创建打印机标签页
        printersTab = QWidget()
        self.tabWidget.addTab(printersTab, "打印机管理")

        # 创建布局
        layout = QVBoxLayout(printersTab)

        # 顶部工具栏
        toolBarLayout = QHBoxLayout()

        # 左侧按钮组
        leftButtonsLayout = QHBoxLayout()

        refreshPrintersBtn = QPushButton("刷新打印机")
        refreshPrintersBtn.setIcon(QIcon("static/icons/refresh.png"))
        refreshPrintersBtn.clicked.connect(self.refreshPrinters)
        leftButtonsLayout.addWidget(refreshPrintersBtn)

        addPrinterBtn = QPushButton("添加打印机")
        addPrinterBtn.setIcon(QIcon("static/icons/add.png"))
        addPrinterBtn.clicked.connect(self.addPrinter)
        leftButtonsLayout.addWidget(addPrinterBtn)

        syncPrintersBtn = QPushButton("同步打印机")
        syncPrintersBtn.setIcon(QIcon("static/icons/sync.png"))
        syncPrintersBtn.clicked.connect(self.syncPrinters)
        leftButtonsLayout.addWidget(syncPrintersBtn)

        toolBarLayout.addLayout(leftButtonsLayout)

        # 右侧搜索框
        searchLayout = QHBoxLayout()
        searchLayout.addWidget(QLabel("搜索:"))
        self.printerSearchEdit = QLineEdit()
        self.printerSearchEdit.setPlaceholderText("输入打印机名称搜索...")
        self.printerSearchEdit.textChanged.connect(self.filterPrinters)
        searchLayout.addWidget(self.printerSearchEdit)

        toolBarLayout.addLayout(searchLayout)
        layout.addLayout(toolBarLayout)

        # 打印机列表
        self.printersTable = QTableWidget(0, 8)
        self.printersTable.setHorizontalHeaderLabels([
            "名称", "系统名称", "状态", "类型", "默认纸张",
            "连接方式", "最后同步", "操作"
        ])

        # 设置表格属性
        self.printersTable.setSelectionBehavior(QTableWidget.SelectRows)
        self.printersTable.setSelectionMode(QTableWidget.SingleSelection)
        self.printersTable.setEditTriggers(QTableWidget.NoEditTriggers)
        self.printersTable.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.printersTable.verticalHeader().setVisible(False)

        # 连接选择信号
        self.printersTable.itemSelectionChanged.connect(self.onPrinterSelected)

        layout.addWidget(self.printersTable)

        # 下方详情区域
        detailsLayout = QHBoxLayout()

        # 左侧打印机详情
        printerDetailGroup = QGroupBox("打印机详情")
        printerDetailLayout = QVBoxLayout(printerDetailGroup)

        self.printerDetailText = QTextEdit()
        self.printerDetailText.setReadOnly(True)
        printerDetailLayout.addWidget(self.printerDetailText)

        detailsLayout.addWidget(printerDetailGroup)

        # 右侧打印机操作
        printerActionsGroup = QGroupBox("打印机操作")
        printerActionsLayout = QVBoxLayout(printerActionsGroup)

        # 状态显示
        statusLayout = QHBoxLayout()
        statusLayout.addWidget(QLabel("当前状态:"))
        self.printerStatusLabel = QLabel("未选择打印机")
        statusLayout.addWidget(self.printerStatusLabel)
        statusLayout.addStretch()
        printerActionsLayout.addLayout(statusLayout)

        # 操作按钮
        actionsLayout = QVBoxLayout()

        setDefaultBtn = QPushButton("设为默认打印机")
        setDefaultBtn.clicked.connect(self.setDefaultPrinter)
        actionsLayout.addWidget(setDefaultBtn)

        testPrintBtn = QPushButton("打印测试页")
        testPrintBtn.clicked.connect(self.printTestPage)
        actionsLayout.addWidget(testPrintBtn)

        configureBtn = QPushButton("配置打印机")
        configureBtn.clicked.connect(self.configurePrinter)
        actionsLayout.addWidget(configureBtn)

        deleteBtn = QPushButton("删除打印机")
        deleteBtn.clicked.connect(self.deletePrinter)
        actionsLayout.addWidget(deleteBtn)

        printerActionsLayout.addLayout(actionsLayout)
        printerActionsLayout.addStretch()

        detailsLayout.addWidget(printerActionsGroup)

        layout.addLayout(detailsLayout)

        # 初始化打印机列表
        self.refreshPrinters()

    def filterPrinters(self, text):
        """根据搜索文本过滤打印机列表"""
        for row in range(self.printersTable.rowCount()):
            name = self.printersTable.item(row, 0).text().lower()
            system_name = self.printersTable.item(row, 1).text().lower()
            match = text.lower() in name or text.lower() in system_name
            self.printersTable.setRowHidden(row, not match)

    def onPrinterSelected(self):
        """处理打印机选择事件"""
        selected_items = self.printersTable.selectedItems()
        if not selected_items:
            self.printerDetailText.clear()
            self.printerStatusLabel.setText("未选择打印机")
            return

        # 获取选中的打印机ID
        row = self.printersTable.currentRow()
        printer_id = self.printersTable.item(row, 0).data(Qt.UserRole)

        # 从数据库获取打印机详情
        printer = self.printer_manager_db.get_printer_by_id(printer_id)
        if not printer:
            return

        # 更新状态标签
        status_map = {0: "离线", 1: "在线", 2: "打印中", 3: "错误"}
        self.printerStatusLabel.setText(status_map.get(printer['status'], "未知"))

        # 更新详情文本
        config = json.loads(printer['config']) if printer['config'] else {}
        detail_text = f"""
打印机信息：
  名称：{printer['name']}
  系统名称：{printer['system_name']}
  打印机ID：{printer['printer_id']}
  后台ID：{printer['backend_id'] or '未同步'}

硬件信息：
  制造商：{config.get('printer_info', {}).get('manufacturer', '未知')}
  型号：{config.get('printer_info', {}).get('model', '未知')}
  驱动：{printer['driver_name'] or '未知'}
  端口：{printer['port_name'] or '未知'}

打印配置：
  默认纸张：{config.get('default_settings', {}).get('paper_size', 'A4')}
  默认颜色模式：{config.get('default_settings', {}).get('color_mode', '黑白')}
  默认双面打印：{config.get('default_settings', {}).get('duplex', False)}
  默认打印质量：{config.get('default_settings', {}).get('quality', '正常')}

支持的功能：
  彩色打印：{'支持' if config.get('supported_features', {}).get('color', False) else '不支持'}
  双面打印：{'支持' if config.get('supported_features', {}).get('duplex', False) else '不支持'}
  支持的纸张：{', '.join(config.get('supported_features', {}).get('paper_sizes', ['A4']))}

系统信息：
  操作系统：{printer['os_type']}
  最后同步时间：{printer['last_sync'] or '未同步'}
  创建时间：{printer['created_at']}
  更新时间：{printer['updated_at']}
"""
        self.printerDetailText.setText(detail_text)

    def refreshPrinters(self):
        """刷新打印机列表"""
        try:
            # 获取所有打印机
            printers = self.printer_manager_db.get_all_printers()

            # 清空表格
            self.printersTable.setRowCount(0)

            # 填充数据
            for printer in printers:
                row = self.printersTable.rowCount()
                self.printersTable.insertRow(row)

                # 解析配置
                config = json.loads(printer['config']) if printer['config'] else {}
                default_settings = config.get('default_settings', {})

                # 状态映射
                status_map = {0: "离线", 1: "在线", 2: "打印中", 3: "错误"}
                status_text = status_map.get(printer['status'], "未知")

                # 设置单元格内容
                self.printersTable.setItem(row, 0, QTableWidgetItem(printer['name']))
                self.printersTable.setItem(row, 1, QTableWidgetItem(printer['system_name']))
                self.printersTable.setItem(row, 2, QTableWidgetItem(status_text))
                self.printersTable.setItem(row, 3, QTableWidgetItem(
                    "彩色" if config.get('supported_features', {}).get('color', False) else "黑白"
                ))
                self.printersTable.setItem(row, 4, QTableWidgetItem(
                    default_settings.get('paper_size', 'A4')
                ))

                # 处理port_name，增加空值检查
                port_name = printer.get('port_name', '')
                connection_type = "网络" if port_name and port_name.startswith('NET') else "USB"
                self.printersTable.setItem(row, 5, QTableWidgetItem(connection_type))

                self.printersTable.setItem(row, 6, QTableWidgetItem(
                    printer['last_sync'] or "未同步"
                ))

                # 创建操作按钮
                actionsWidget = QWidget()
                actionsLayout = QHBoxLayout(actionsWidget)
                actionsLayout.setContentsMargins(0, 0, 0, 0)

                editBtn = QPushButton("编辑")
                editBtn.setProperty("printer_id", printer['printer_id'])
                editBtn.clicked.connect(lambda checked, pid=printer['printer_id']: self.editPrinter(pid))
                actionsLayout.addWidget(editBtn)

                testBtn = QPushButton("测试")
                testBtn.setProperty("printer_id", printer['printer_id'])
                testBtn.clicked.connect(lambda checked, pid=printer['printer_id']: self.testPrinter(pid))
                actionsLayout.addWidget(testBtn)

                self.printersTable.setCellWidget(row, 7, actionsWidget)

                # 存储打印机ID
                self.printersTable.item(row, 0).setData(Qt.UserRole, printer['printer_id'])

            logger.info(f"已刷新打印机列表，共{len(printers)}台打印机")

        except Exception as e:
            logger.error(f"刷新打印机列表失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"刷新打印机列表失败: {str(e)}")

    def syncPrinters(self):
        """同步打印机信息到后台"""
        try:
            # 获取所有未同步的打印机
            printers = self.printer_manager_db.get_all_printers()
            unsync_printers = [p for p in printers if p['sync_status'] != 1]

            if not unsync_printers:
                QMessageBox.information(self, "同步打印机", "所有打印机都已同步")
                return

            # 同步每台打印机
            success_count = 0
            for printer in unsync_printers:
                try:
                    # 调用后台API同步打印机信息
                    success, response = self.api_client.sync_printer(printer)
                    if success:
                        # 更新本地数据库
                        self.printer_manager_db.update_printer_sync_status(
                            printer['printer_id'],
                            response['printer_id'],
                            1  # 同步成功
                        )
                        success_count += 1
                    else:
                        logger.error(f"同步打印机失败: {printer['name']} - {response}")
                except Exception as e:
                    logger.error(f"同步打印机出错: {printer['name']} - {str(e)}")

            # 刷新显示
            self.refreshPrinters()

            # 显示结果
            QMessageBox.information(
                self,
                "同步完成",
                f"成功同步{success_count}台打印机，"
                f"失败{len(unsync_printers) - success_count}台"
            )

        except Exception as e:
            logger.error(f"同步打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"同步打印机失败: {str(e)}")

    def setDefaultPrinter(self):
        """设置默认打印机"""
        selected_items = self.printersTable.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择一台打印机")
            return

        row = self.printersTable.currentRow()
        printer_id = self.printersTable.item(row, 0).data(Qt.UserRole)

        try:
            # 获取打印机信息
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 调用打印管理器设置默认打印机
            success = self.print_manager.set_default_printer(printer['system_name'])

            if success:
                QMessageBox.information(self, "成功", f"已将 '{printer['name']}' 设置为默认打印机")
                logger.info(f"已将 '{printer['name']}' 设置为默认打印机")
            else:
                raise Exception("设置默认打印机失败")

        except Exception as e:
            logger.error(f"设置默认打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置默认打印机失败: {str(e)}")

    def printTestPage(self):
        """打印测试页"""
        selected_items = self.printersTable.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择一台打印机")
            return

        row = self.printersTable.currentRow()
        printer_id = self.printersTable.item(row, 0).data(Qt.UserRole)

        try:
            # 获取打印机信息
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 调用打印管理器打印测试页
            success = self.print_manager.print_test_page(printer['system_name'])

            if success:
                QMessageBox.information(self, "成功", f"测试页已发送到打印机 '{printer['name']}'")
                logger.info(f"测试页已发送到打印机 '{printer['name']}'")
            else:
                raise Exception("打印测试页失败")

        except Exception as e:
            logger.error(f"打印测试页失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打印测试页失败: {str(e)}")

    def configurePrinter(self):
        """配置打印机"""
        selected_items = self.printersTable.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择一台打印机")
            return

        row = self.printersTable.currentRow()
        printer_id = self.printersTable.item(row, 0).data(Qt.UserRole)

        try:
            # 获取打印机信息
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 显示配置对话框
            dialog = ConfigurePrinterDialog(printer, self)
            if dialog.exec_() == ConfigurePrinterDialog.Accepted:
                printer_data = dialog.get_printer_data()
                if printer_data:
                    # 更新数据库
                    self.printer_manager_db.update_printer_config(
                        printer_id,
                        printer_data['config']
                    )

                    # 刷新显示
                    self.refreshPrinters()

                    QMessageBox.information(self, "成功", "打印机配置已更新")
                    logger.info(f"更新打印机配置成功: {printer['name']}")

        except Exception as e:
            logger.error(f"配置打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"配置打印机失败: {str(e)}")

    def deletePrinter(self):
        """删除打印机"""
        selected_items = self.printersTable.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请先选择一台打印机")
            return

        row = self.printersTable.currentRow()
        printer_id = self.printersTable.item(row, 0).data(Qt.UserRole)

        try:
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f'确定要删除打印机"{printer["name"]}"吗？\n此操作不可恢复。',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 删除打印机
                self.printer_manager_db.delete_printer(printer_id)

                # 刷新显示
                self.refreshPrinters()

                QMessageBox.information(self, "成功", "打印机已删除")

        except Exception as e:
            logger.error(f"删除打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"删除打印机失败: {str(e)}")

    def createJobsTab(self):
        """创建任务标签页"""
        jobsTab = QWidget()
        self.tabWidget.addTab(jobsTab, "打印任务")

        # 创建布局
        layout = QVBoxLayout(jobsTab)

        # 创建打印任务对话框
        self.taskDialog = PrintTaskDialog(self)
        layout.addWidget(self.taskDialog)

    def createSettingsTab(self):
        # 创建设置标签页
        settingsTab = QWidget()
        self.tabWidget.addTab(settingsTab, "设置")

        # 创建布局
        layout = QVBoxLayout(settingsTab)

        # 添加云连接设置组
        cloudSettingsGroup = QGroupBox("云连接设置")
        cloudSettingsLayout = QVBoxLayout(cloudSettingsGroup)

        # 添加设置项
        cloudSettingsLayout.addWidget(QLabel("云服务器地址:"))
        self.cloudServerEdit = QTextEdit()
        self.cloudServerEdit.setMaximumHeight(30)
        self.cloudServerEdit.setText(self.config_manager.get_api_url())
        cloudSettingsLayout.addWidget(self.cloudServerEdit)

        cloudSettingsLayout.addWidget(QLabel("API密钥:"))
        self.apiKeyEdit = QTextEdit()
        self.apiKeyEdit.setMaximumHeight(30)
        self.apiKeyEdit.setText("your-api-key-here")
        cloudSettingsLayout.addWidget(self.apiKeyEdit)

        layout.addWidget(cloudSettingsGroup)

        # 添加本地设置组
        localSettingsGroup = QGroupBox("本地设置")
        localSettingsLayout = QVBoxLayout(localSettingsGroup)

        # 添加设置项
        localSettingsLayout.addWidget(QLabel("临时文件目录:"))
        self.tempDirEdit = QTextEdit()
        self.tempDirEdit.setMaximumHeight(30)
        self.tempDirEdit.setText("C:/PrintEase/temp")
        localSettingsLayout.addWidget(self.tempDirEdit)

        localSettingsLayout.addWidget(QLabel("日志级别:"))
        self.logLevelCombo = QComboBox()
        self.logLevelCombo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.logLevelCombo.setCurrentIndex(1)  # 默认INFO
        localSettingsLayout.addWidget(self.logLevelCombo)

        layout.addWidget(localSettingsGroup)

        # 添加按钮
        buttonsLayout = QHBoxLayout()

        saveSettingsBtn = QPushButton("保存设置")
        saveSettingsBtn.clicked.connect(self.saveSettings)
        buttonsLayout.addWidget(saveSettingsBtn)

        resetSettingsBtn = QPushButton("重置设置")
        resetSettingsBtn.clicked.connect(self.resetSettings)
        buttonsLayout.addWidget(resetSettingsBtn)

        buttonsLayout.addStretch()

        layout.addLayout(buttonsLayout)
        layout.addStretch()

    def createLogsTab(self):
        # 创建日志标签页
        logsTab = QWidget()
        self.tabWidget.addTab(logsTab, "日志")

        # 创建布局
        layout = QVBoxLayout(logsTab)

        # 添加操作按钮
        buttonsLayout = QHBoxLayout()

        refreshLogsBtn = QPushButton("刷新日志")
        refreshLogsBtn.clicked.connect(self.refreshLogs)
        buttonsLayout.addWidget(refreshLogsBtn)

        clearLogsBtn = QPushButton("清除日志")
        clearLogsBtn.clicked.connect(self.clearLogs)
        buttonsLayout.addWidget(clearLogsBtn)

        # 添加日志级别选择
        buttonsLayout.addWidget(QLabel("显示级别:"))
        self.logFilterCombo = QComboBox()
        self.logFilterCombo.addItems(["全部", "INFO以上", "WARNING以上", "ERROR"])
        buttonsLayout.addWidget(self.logFilterCombo)

        buttonsLayout.addStretch()

        layout.addLayout(buttonsLayout)

        # 添加日志文本区域
        self.logTextEdit = QTextEdit()
        self.logTextEdit.setReadOnly(True)
        self.logTextEdit.setFont(QFont("Courier New", 9))
        layout.addWidget(self.logTextEdit)

    # 功能方法
    def updateStatus(self):
        # 更新状态信息（此处为示例，实际应从系统获取）
        self.statusLabel.setText("系统运行中...")
        self.cloudStatusLabel.setText("云连接状态: 已连接")
        self.cloudDetailStatusLabel.setText("已连接 - 最后心跳: 2023-03-08 12:34:56")
        self.printerStatusLabel.setText("在线: 2 | 离线: 1 | 错误: 0")
        self.jobStatusLabel.setText("等待: 1 | 打印中: 1 | 完成: 10 | 失败: 0")

        # 在日志中添加一条信息
        self.logTextEdit.append("[INFO] [2023-03-08 12:34:56] 状态更新完成")

    def refreshAll(self):
        self.refreshPrinters()
        self.refreshJobs()
        self.refreshLogs()
        QMessageBox.information(self, "刷新", "所有数据已刷新")

    def connectToCloud(self):
        # 连接到云端
        QMessageBox.information(self, "云连接", "正在连接到云端...")
        # 实际连接代码

    def disconnectFromCloud(self):
        # 断开云端连接
        QMessageBox.information(self, "云连接", "已断开云端连接")
        # 实际断开连接代码

    def showHelp(self):
        # 显示帮助信息
        QMessageBox.information(self, "帮助", "PrintEase 打印代理程序 v1.0\n\n"
                               "本程序用于连接云端打印服务和本地打印机，实现远程打印功能。\n\n"
                               "如需帮助，请联系技术支持。")

    def refreshJobs(self):
        # 刷新任务列表
        QMessageBox.information(self, "刷新任务", "正在刷新任务列表...")
        # 实际刷新代码

    def saveSettings(self):
        # 保存设置
        QMessageBox.information(self, "设置", "设置已保存")
        # 实际保存代码

    def resetSettings(self):
        # 重置设置
        QMessageBox.information(self, "设置", "设置已重置为默认值")
        # 实际重置代码

    def refreshLogs(self):
        # 刷新日志
        QMessageBox.information(self, "刷新日志", "正在刷新日志...")
        # 实际刷新代码

    def clearLogs(self):
        # 清除日志
        self.logTextEdit.clear()
        QMessageBox.information(self, "清除日志", "日志已清除")

    # 添加登录对话框方法
    def showLoginDialog(self):
        dialog = LoginDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            username, password, remember_me = dialog.getCredentials()
            self.performLogin(username, password, remember_me)

    # 添加登录方法
    def performLogin(self, username, password, remember_me):
        """执行登录操作"""
        try:
            # 调用登录接口
            success, response = self.api_client.login(username, password)

            if success:
                # 保存认证信息到数据库
                dialog = self.findChild(LoginDialog)
                if dialog:
                    dialog.save_auth_info(response)

                # 设置API客户端的token
                self.api_client.token = response.get('data', {}).get('token')

                # 设置WebSocket客户端
                self.setupWebSocketClient()

                # 更新状态栏
                merchant = response.get('data', {}).get('merchant', {})
                self.statusLabel.setText(f"已登录: {merchant.get('real_name')}")

                # 保存商家信息
                self.api_client.merchant = merchant

                # 更新商家信息显示
                self.updateMerchantInfo()

                logger.info("登录成功")
            else:
                QMessageBox.warning(self, "登录失败", str(response))
                self.showLoginDialog()
        except Exception as e:
            logger.error(f"登录失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"登录时发生错误: {str(e)}")
            self.showLoginDialog()

    # 添加更新商家信息方法
    def updateMerchantInfo(self):
        """更新商家信息显示"""
        try:
            # 从token中解析商家信息
            decoded = jwt.decode(self.api_client.token, options={"verify_signature": False})
            merchant_info = {
                'store_name': decoded.get('store_name', ''),
                'username': decoded.get('username', ''),
                'role_type': decoded.get('role_type', ''),
                'store_id': decoded.get('store_id', 0)
            }

            # 更新系统信息显示
            system_info = (
                f"系统版本: PrintEase Agent v1.0\n"
                f"Python版本: 3.10.11\n"
                f"操作系统: Windows 10\n"
                f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"\n商家信息:\n"
                f"用户名: {merchant_info['username']}\n"
                f"店铺名称: {merchant_info['store_name']}\n"
                f"角色类型: {'店长' if merchant_info['role_type'] == 'owner' else '员工'}"
            )

            self.sysInfoText.setText(system_info)
            logger.info("商家信息已更新")

        except Exception as e:
            logger.error(f"更新商家信息失败: {str(e)}")
            self.sysInfoText.setText("获取商家信息失败，请重新登录")

    # 添加登出方法
    def logout(self):
        """用户登出处理"""
        if self.ws_client:
            self.ws_client.stop()
            self.ws_client = None

        # 清除API客户端的token
        self.api_client.token = None

        # 显示登录对话框
        self.showLoginDialog()

        logger.info("用户已登出")

    def connectMessageHandlerSignals(self):
        """连接消息处理器的信号"""
        self.message_handler.print_task_received.connect(self.onPrintTaskReceived)
        self.message_handler.auth_response.connect(self.onAuthResponse)
        self.message_handler.error_occurred.connect(self.onMessageError)

    def connectFileDownloaderSignals(self):
        """连接文件下载器的信号"""
        self.file_downloader.download_started.connect(self.onDownloadStarted)
        self.file_downloader.download_progress.connect(self.onDownloadProgress)
        self.file_downloader.download_completed.connect(self.onDownloadCompleted)
        self.file_downloader.download_failed.connect(self.onDownloadFailed)

    def setupWebSocketClient(self):
        """设置WebSocket客户端"""
        # 检查是否已登录
        if not self.api_client.is_logged_in():
            logging.warning("未登录，无法设置WebSocket客户端")
            return

        # 获取商家ID
        merchant_id = self.config_manager.get_merchant_id()
        if not merchant_id:
            logging.warning("无法获取商家ID，无法设置WebSocket客户端")
            QMessageBox.warning(self, "警告", "无法获取商家ID，无法接收打印任务。请重新登录。")
            return

        logging.info(f"设置WebSocket客户端，商家ID: {merchant_id}")

        # 获取WebSocket配置
        ws_config = self.config_manager.get_websocket_config()
        if not ws_config.get("enabled", True):
            logging.info("WebSocket已禁用")
            return

        # 如果已有WebSocket客户端，先停止它
        if self.ws_client:
            self.ws_client.stop()

        # 创建新的WebSocket客户端
        api_url = self.config_manager.get_api_url()
        token = self.api_client.token

        try:
            self.ws_client = WebSocketClient(api_url, merchant_id, token)

            # 设置心跳间隔
            heartbeat_interval = ws_config.get("heartbeat_interval", 30)
            self.ws_client.heartbeat_interval = heartbeat_interval

            # 设置重连参数
            reconnect_delay = ws_config.get("reconnect_delay", 1)
            max_reconnect_delay = ws_config.get("max_reconnect_delay", 60)
            self.ws_client.reconnect_delay = reconnect_delay
            self.ws_client.max_reconnect_delay = max_reconnect_delay

            # 连接信号
            self.ws_client.connected.connect(self.onWebSocketConnected)
            self.ws_client.disconnected.connect(self.onWebSocketDisconnected)
            self.ws_client.message_received.connect(self.onWebSocketMessageReceived)
            self.ws_client.connection_error.connect(self.onWebSocketError)

            # 启动WebSocket客户端
            self.ws_client.start()
            logging.info("WebSocket客户端已启动")
        except Exception as e:
            logging.error(f"设置WebSocket客户端失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"设置WebSocket客户端失败: {str(e)}")

    def onWebSocketConnected(self):
        """WebSocket连接成功"""
        logging.info("WebSocket已连接")
        self.updateStatus()

    def onWebSocketDisconnected(self, reason):
        """WebSocket断开连接"""
        logging.info(f"WebSocket已断开: {reason}")
        self.updateStatus()

    def onWebSocketMessageReceived(self, message):
        """收到WebSocket消息"""
        logging.debug(f"收到WebSocket消息: {message}")
        self.message_handler.handle_message(message)

    def onWebSocketError(self, error):
        """处理WebSocket错误"""
        logger.error(f"WebSocket错误: {error}")
        # 后面我们和后台配合,设计一个被踢下线的消息
        if "token expired" in str(error).lower() or "invalid token" in str(error).lower():
            # Token失效，但不自动重新登录
            QMessageBox.warning(self, "登录失效", "代理程序在别的电脑登录，请重新启动程序")
            self.close()
            sys.exit(0)
        else:
            self.cloudStatusLabel.setText("云连接状态: 连接错误")
            self.cloudDetailStatusLabel.setText(f"错误: {error}")

    def onPrintTaskReceived(self, task):
        """收到打印任务"""
        task_id = task.get('task_id')
        logging.info(f"收到打印任务: {task_id}")

        # 将任务添加到打印任务处理器
        self.task_processor.add_task(task_id)

        # 更新UI显示
        self.refreshJobs()

        # 发送任务接收确认
        if self.ws_client and self.ws_client.is_connected:
            self.ws_client.send_status_update(
                task_id,
                "received",
                "任务已接收"
            )

    def onAuthResponse(self, success, message):
        """认证响应"""
        if success:
            logging.info("WebSocket认证成功")
        else:
            logging.error(f"WebSocket认证失败: {message}")
            QMessageBox.warning(self, "认证失败", f"WebSocket认证失败: {message}")

    def onMessageError(self, error):
        """消息处理错误"""
        logging.error(f"消息处理错误: {error}")

    def onDownloadStarted(self, url, save_path):
        """文件下载开始"""
        logging.info(f"开始下载文件: {url} -> {save_path}")
        # 可以在UI中显示下载进度

    def onDownloadProgress(self, url, current, total):
        """文件下载进度"""
        if total > 0:
            percent = int(current * 100 / total)
            logging.debug(f"下载进度: {percent}%")
            # 可以更新UI中的进度条

    def onDownloadCompleted(self, url, save_path):
        """文件下载完成"""
        logging.info(f"文件下载完成: {save_path}")
        # 可以在UI中显示下载完成通知

    def onDownloadFailed(self, url, error):
        """文件下载失败"""
        logging.error(f"文件下载失败: {error}")
        # 可以在UI中显示下载失败通知

    def check_auth_status(self):
        """检查认证状态"""
        try:
            # 获取最新的认证记录
            auth = self.auth_manager_db.get_latest_auth()

            if not auth:
                logger.info("未找到认证记录，显示登录对话框")
                self.showLoginDialog()
                return

            # 检查是否是同一设备
            if auth['device_id'] != self.device_id:
                logger.info("设备ID不匹配，需要重新登录")
                self.showLoginDialog()
                return

            # 检查token是否过期
            try:
                # 尝试解析不同格式的时间戳
                if 'T' in auth['token_expires_at']:
                    # ISO格式 (2025-05-17T00:21:40.274007)
                    expires_at = datetime.fromisoformat(auth['token_expires_at'].split('.')[0] if '.' in auth['token_expires_at'] else auth['token_expires_at'])
                else:
                    # SQLite格式 (2025-05-17 00:21:40)
                    expires_at = datetime.strptime(auth['token_expires_at'], '%Y-%m-%d %H:%M:%S')

                if datetime.now() >= expires_at:
                    logger.info("Token已过期，需要重新登录")
                    self.showLoginDialog()
                    return
            except Exception as e:
                logger.error(f"解析token过期时间失败: {str(e)}")
                self.showLoginDialog()
                return

            # 设置API客户端的token
            self.api_client.token = auth['token']

            # 验证token有效性
            success, message = self.api_client.verify_token()
            if success:
                logger.info("Token验证成功")
                # 设置WebSocket客户端
                self.setupWebSocketClient()
                # 更新状态栏
                self.statusLabel.setText(f"已登录: {auth['merchant_real_name']}")

                # 获取并保存商家信息
                merchant_info = {
                    'username': auth.get('merchant_username', ''),
                    'real_name': auth.get('merchant_real_name', ''),
                    'merchant_type': auth.get('merchant_type', ''),
                    'store_id': auth.get('store_id', 0)
                }
                self.api_client.merchant = merchant_info

                # 更新商家信息显示
                self.updateMerchantInfo()
            else:
                logger.error(f"Token验证失败: {message}")
                self.showLoginDialog()
        except Exception as e:
            logger.error(f"检查认证状态失败: {str(e)}")
            self.showLoginDialog()

    def addPrinter(self):
        """添加打印机"""
        dialog = AddPrinterDialog(self)
        if dialog.exec_() == AddPrinterDialog.Accepted:
            printer_data = dialog.get_printer_data()
            if printer_data:
                try:
                    # 生成打印机ID（使用系统名称的MD5）
                    import hashlib
                    printer_id = hashlib.md5(
                        printer_data['system_name'].encode('utf-8')
                    ).hexdigest()

                    # 添加到数据库
                    self.printer_manager_db.add_printer(
                        name=printer_data['name'],
                        printer_id=printer_id,
                        system_name=printer_data['system_name'],
                        os_type='windows',  # TODO: 根据实际系统类型设置
                        config=printer_data['config']
                    )

                    # 刷新显示
                    self.refreshPrinters()

                    QMessageBox.information(self, "成功", "打印机添加成功")
                    logger.info(f"添加打印机成功: {printer_data['name']}")

                except Exception as e:
                    logger.error(f"添加打印机失败: {str(e)}")
                    QMessageBox.critical(self, "错误", f"添加打印机失败: {str(e)}")

    def editPrinter(self, printer_id):
        """编辑打印机"""
        try:
            # 获取打印机信息
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 显示编辑对话框
            dialog = EditPrinterDialog(printer, self)
            if dialog.exec_() == EditPrinterDialog.Accepted:
                printer_data = dialog.get_printer_data()
                if printer_data:
                    # 更新打印机配置
                    self.printer_manager_db.update_printer_config(
                        printer_id,
                        printer_data['config']
                    )

                    # 更新打印机名称
                    self.printer_manager_db.update_printer_name(
                        printer_id,
                        printer_data['name']
                    )

                    # 刷新显示
                    self.refreshPrinters()

                    QMessageBox.information(self, "成功", "打印机更新成功")
                    logger.info(f"更新打印机成功: {printer_data['name']}")

        except Exception as e:
            logger.error(f"编辑打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"编辑打印机失败: {str(e)}")

    def testPrinter(self, printer_id):
        """测试打印机"""
        try:
            # 获取打印机信息
            printer = self.printer_manager_db.get_printer_by_id(printer_id)
            if not printer:
                raise Exception("打印机不存在")

            # 调用打印管理器打印测试页
            success = self.print_manager.print_test_page(printer['system_name'])

            if success:
                QMessageBox.information(self, "成功", f"测试页已发送到打印机 '{printer['name']}'")
                logger.info(f"测试页已发送到打印机 '{printer['name']}'")
            else:
                raise Exception("打印测试页失败")

        except Exception as e:
            logger.error(f"测试打印机失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"测试打印机失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        # 停止打印任务处理线程
        if hasattr(self, 'task_processor'):
            self.task_processor.stop()
            logger.info("打印任务处理线程已停止")

        # 停止WebSocket客户端
        if self.ws_client:
            self.ws_client.stop()
            logger.info("WebSocket客户端已停止")

        # 接受关闭事件
        event.accept()

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格，在所有平台上看起来一致

    window = MainWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()



