package handlers

import (
	"PrintEaseBackend/internal/config"
	"PrintEaseBackend/internal/services"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type StoreHandler struct {
	storeService *services.StoreService
}

func NewStoreHandler() *StoreHandler {
	return &StoreHandler{
		storeService: &services.StoreService{},
	}
}

// GetStores 获取店铺列表
func GetStores(c *gin.Context) {
	// 获取请求参数
	longitude, err := strconv.ParseFloat(c.Query("longitude"), 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "经度参数无效",
			"err":  err.Error(),
		})
		return
	}

	latitude, err := strconv.ParseFloat(c.Query("latitude"), 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "纬度参数无效",
			"err":  err.Error(),
		})
		return
	}

	// 使用优化后的方法直接获取范围内的已排序店铺
	storeService := &services.StoreService{}
	stores, err := storeService.GetNearbyStores(
		latitude,
		longitude,
		config.GlobalConfig.Store.MaxDistance,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "获取店铺列表失败",
			"err":  err.Error(),
		})
		return
	}

	// 转换数据格式
	var storeList []gin.H
	for _, store := range stores {
		status := "closed"
		if store.Status == "1" {
			status = "open"
		}

		storeList = append(storeList, gin.H{
			"id":            store.ID,
			"name":          store.Name,
			"address":       store.Address,
			"distance":      fmt.Sprintf("%.1f", store.Distance),
			"businessHours": store.BusinessHours,
			"status":        status,
			"latitude":      store.Latitude,
			"longitude":     store.Longitude,
			"announcement":  store.Announcement,
			"phone":         store.Phone,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": storeList,
	})
}
