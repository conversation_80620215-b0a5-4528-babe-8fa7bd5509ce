# 项目介绍
    --自助打印代理程序PrintEaseAgent。
    --采用Python3语言开发，PyQt5框架开发图形界面。
    --该系统代码放在/PrintEaseAgent目录下。

# 项目描述
    由于后台PrintEaseBackend无法直接与处于局域网内的打印机进行通信，因此需要一个代理程序PrintEaseAgent，该代理程序负责将后台接收到的打印请求转发给打印机。代理程序安装在与打印店内打印机同一局域网的电脑上，用于管理打印机，以及转发后台接收到的打印请求给打印机。      

# 代码结构
    PrintEaseAgent/
        ├── main.py                 # 主程序入口
        ├── config.json             # 配置文件（包含数据库路径等配置）
        ├── requirements.txt        # 依赖包列表
        ├── ui/                     # UI相关文件
        │   ├── main_window.py      # 主窗口
        │   ├── login_dialog.py     # 登录对话框
        │   └── dialogs/            # 其他对话框
        ├── core/                   # 核心功能
        │   ├── api_client.py       # API客户端
        │   ├── websocket_client.py # WebSocket客户端
        │   ├── message_handler.py  # 消息处理器
        │   ├── print_manager.py    # 打印管理
        │   └── job_queue.py        # 任务队列
        ├── utils/                  # 工具函数
        │   ├── config_manager.py   # 配置管理
        │   ├── database_manager.py # 数据库管理
        │   ├── file_downloader.py  # 文件下载
        │   └── log_utils.py        # 日志工具
        ├── data/                   # 数据存储目录（默认）
        │   └── printease.db        # SQLite数据库文件（默认）
        ├── logs/                   # 日志文件目录
        ├── uploads/                # 上传文件保存目录
        └── resources/              # 资源文件
            └── icons/              # 图标
        
# 配置文件说明
    config.json文件包含以下主要配置：
    ```json
    {
        "database": {
            "path": "data/printease.db"  # 数据库文件路径
        },
        "api": {
            "base_url": "http://localhost:8080",  # 后端API地址
            "websocket_url": "ws://localhost:8080/ws"  # WebSocket地址
        },
        "log": {
            "level": "INFO",  # 日志级别
            "path": "logs"    # 日志目录
        }
    }
    ```

# 项目环境
    # 安装Python3.10.11
    https://www.python.org/downloads/windows/

    # 安装依赖包
    pip install -r requirements.txt

    # 推荐开发工具
    - Cursor IDE（推荐）：支持代码智能提示、Git集成等
    - DB Browser for SQLite：用于查看和编辑SQLite数据库
    - Postman：用于测试API接口

# 功能特性
    1. 用户认证：与PrintEaseBackend进行认证，获取访问令牌
    2. WebSocket通信：与后端建立WebSocket连接，实时接收打印任务
    3. 文件下载：从后端下载打印文件
    4. 打印管理：管理本地打印机和打印任务
    5. 日志记录：记录系统运行日志
    6. 数据存储：使用SQLite数据库存储认证信息和配置

# 数据库设计
    1. 数据库配置：
        - 配置文件：config.json
        - 默认路径：data/printease.db
        - 支持自定义路径：修改config.json中的database.path
        - 类型：SQLite3
        
    2. 表结构：
        2.1 auth表 - 存储认证信息
        ```sql
        CREATE TABLE auth (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            merchant_id INTEGER NOT NULL,
            token TEXT NOT NULL,
            username TEXT NOT NULL,
            password TEXT,
            store_name TEXT,
            merchant_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ```

# WebSocket通信
    代理程序通过WebSocket与后端保持长连接，实时接收打印任务。通信流程如下：
    
    1. 连接建立：代理程序启动后，使用商家ID和认证令牌与后端建立WebSocket连接
    2. 认证：连接建立后，发送认证消息进行身份验证
    3. 心跳维护：定期发送心跳消息，保持连接活跃
    4. 任务接收：接收后端发送的打印任务消息
    5. 文件下载：根据任务消息中的文件URL下载打印文件
    6. 状态反馈：向后端发送任务状态更新

# 认证系统
    1. 认证流程：
        1.1 代理程序认证：
            - 使用用户名和密码进行登录认证
            - 登录成功后获取JWT Token
            - Token中包含：merchant_id, store_id, username, merchant_type等信息
            - 支持Token自动续期和自动重新登录
            - 支持记住密码功能

        1.2 WebSocket认证：
            - 使用Bearer Token认证
            - 连接建立时需要在URL中包含merchant_id
            - 连接成功后发送auth消息进行二次认证
            - 支持心跳机制保持连接
            - 支持断线自动重连

        1.3 API认证：
            - 使用Bearer Token认证
            - 在请求头中携带Token
            - 支持Token过期检查
            - 支持自动重新登录

    2. 安全特性：
        2.1 Token管理：
            - 使用JWT（JSON Web Token）进行身份验证
            - Token包含过期时间（exp）
            - Token存储在SQLite数据库中
            - 支持Token解析和验证

        2.2 密码安全：
            - 密码仅在选择"记住密码"时保存
            - 密码保存在SQLite数据库中
            - 建议后续增加密码加密存储

        2.3 通信安全：
            - 支持HTTPS安全传输
            - 支持WSS安全WebSocket
            - API请求自动携带认证信息

    3. 异常处理：
        3.1 认证异常：
            - Token解析失败处理
            - 登录失败重试
            - Token过期自动续期
            
        3.2 连接异常：
            - WebSocket断线重连
            - 使用指数退避算法
            - 详细的错误日志记录
            
# 消息格式
    1. 认证消息：
    ```json
    {
      "type": "auth",
      "data": {
        "merchant_id": 123,
        "token": "认证令牌",
        "agent_version": "1.0.0",
        "agent_name": "Store_123_Agent"
      }
    }
    ```
    
    2. 心跳消息：
    ```json
    {
      "type": "heartbeat",
      "timestamp": 1623456789
    }
    ```
    
    3. 打印任务消息：
    ```json
    {
      "type": "print_task",
      "task_id": "task_12345",
      "data": {
        "order_id": 67890,
        "file_info": {
          "file_id": 12345,
          "filename": "document.pdf",
          "file_type": "pdf",
          "file_size": 1024567,
          "download_url": "http://backend/api/download?file_id=12345&token=xxx"
        },
        "print_config": {
          "copies": 1,
          "color_mode": "color",
          "paper_size": "A4",
          "duplex": true,
          "pages": "all",
          "orientation": "portrait"
        }
      }
    }
    ```
    
    4. 状态更新消息：
    ```json
    {
      "type": "status_update",
      "task_id": "task_12345",
      "status": "received",
      "message": "任务已接收",
      "timestamp": 1623456789
    }
    ```

# 运行方式
    1. 确保已安装所有依赖：
       pip install -r requirements.txt

    2. 启动程序：
       python main.py

# 调试工具
    1. 数据库查看
        - 使用 DB Browser for SQLite 查看数据库内容
        - 数据库文件位置：由config.json中的database.path指定
        - 默认位置：PrintEaseAgent/data/printease.db
        
    2. 日志查看
        - 日志文件位置：PrintEaseAgent/logs/
        - 使用文本编辑器查看日志文件
        
    3. API测试
        - 使用Postman测试API接口
        - 使用Chrome开发者工具查看WebSocket通信

# 打印机管理
    1. 数据库设计
        1.1 打印机表(printers)：
        ```sql
        CREATE TABLE printers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,              -- 打印机名称
            status TEXT DEFAULT 'offline',    -- 打印机状态：online/offline/error
            type TEXT DEFAULT 'normal',       -- 打印机类型：normal/color/photo等
            description TEXT,                 -- 打印机描述
            capabilities TEXT,                -- 打印机功能，JSON格式存储
            last_online TIMESTAMP,           -- 最后在线时间
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        ```

    2. 界面功能
        2.1 打印机列表：
            - 显示所有打印机基本信息
            - 支持按名称搜索打印机
            - 显示打印机在线状态
            - 支持按类型筛选打印机
            
        2.2 打印机管理：
            - 添加新打印机
            - 编辑打印机信息
            - 删除打印机
            - 启用/禁用打印机
            
        2.3 状态监控：
            - 实时监控打印机状态
            - 显示打印机错误信息
            - 显示打印机队列状态
            
    3. 数据同步
        3.1 本地同步：
            - 定期检查打印机在线状态
            - 自动更新打印机状态
            - 记录打印机状态变更日志
            
        3.2 远程同步：
            - 通过WebSocket实时同步打印机状态到后台
            - 接收后台的打印机配置更新
            - 支持远程打印机管理

    4. 错误处理
        4.1 打印机异常：
            - 打印机离线处理
            - 打印机卡纸处理
            - 打印机缺纸处理
            - 其他硬件错误处理
            
        4.2 任务异常：
            - 打印任务失败重试
            - 打印队列阻塞处理
            - 文件格式错误处理
            
    5. 打印机接口
        5.1 基本操作：
            - 获取打印机列表
            - 获取打印机状态
            - 获取打印队列
            - 取消打印任务
            
        5.2 高级功能：
            - 获取打印机详细信息
            - 配置打印机参数
            - 清理打印队列
            - 重置打印机
