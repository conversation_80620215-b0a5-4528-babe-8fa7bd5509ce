package handlers

import (
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// 定义购物车状态列表，用于查询
// var statusList = []int8{0, 1, 2} // 包含所有需要查询的状态
var pendingStatus = []int8{models.CartStatusPending} // 只查询待处理的项目

// CartHandler 购物车处理器
type CartHandler struct {
	// 如果需要服务层，可以添加
	// cartService *services.CartService
}

// NewCartHandler 创建购物车处理器
func NewCartHandler() *CartHandler {
	return &CartHandler{
		// cartService: &services.CartService{},
	}
}

// GetCartItems 获取用户的购物车项目
func (h *CartHandler) GetCartItems(c *gin.Context) {
	// 获取用户ID参数
	userID, err := strconv.ParseUint(c.Query("user_id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的用户ID",
		})
		return
	}

	// 查询购物车项目
	var cartItems []models.CartItem
	result := database.DB.Where("user_id = ? AND status IN (?)", userID, pendingStatus).
		Preload("File").
		Order("created_at DESC").
		Find(&cartItems)

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "查询购物车项目失败: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "success",
		"data": cartItems,
	})
}

// UpdateCartItemStatusRequest 更新状态请求
type UpdateCartItemStatusRequest struct {
	Status int8 `json:"status"`
}

// BatchUpdateCartStatusRequest 批量更新状态请求
type BatchUpdateCartStatusRequest struct {
	IDs    []uint64 `json:"ids"`
	Status int8     `json:"status"`
}

// UpdateCartItemStatus 更新单个购物车项状态
func (h *CartHandler) UpdateCartItemStatus(c *gin.Context) {
	// 获取购物车项ID
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的购物车项ID",
		})
		return
	}

	// 解析请求体
	var req UpdateCartItemStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的请求参数",
		})
		return
	}

	// 更新状态
	if err := database.DB.Model(&models.CartItem{}).
		Where("id = ?", id).
		Update("status", req.Status).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "更新状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "更新成功",
	})
}

// BatchUpdateCartStatus 批量更新购物车项状态
func (h *CartHandler) BatchUpdateCartStatus(c *gin.Context) {
	// 解析请求体
	var req BatchUpdateCartStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的请求参数",
		})
		return
	}

	// 检查参数
	if len(req.IDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "购物车项ID列表不能为空",
		})
		return
	}

	// 批量更新状态
	if err := database.DB.Model(&models.CartItem{}).
		Where("id IN ?", req.IDs).
		Update("status", req.Status).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "批量更新状态失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "批量更新成功",
	})
}

// UpdateCartItemSettings 更新购物车项目的打印设置
func (h *CartHandler) UpdateCartItemSettings(c *gin.Context) {
	// 获取路径参数中的ID
	id, err := strconv.ParseUint(c.Param("id"), 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "无效的购物车项ID",
		})
		return
	}

	// 先查找该购物车项是否存在
	var cartItem models.CartItem
	if err := database.DB.First(&cartItem, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code": -1,
			"msg":  "购物车项不存在",
		})
		return
	}

	// 解析请求体
	var settings struct {
		PaperSize     string `json:"paper_size"`
		ColorMode     string `json:"color_mode"`
		SideMode      string `json:"side_mode"`
		Copies        int    `json:"copies"`
		StartPage     int    `json:"start_page"`
		EndPage       int    `json:"end_page"`
		PagesPerSheet int    `json:"pages_per_sheet"`
		Config        string `json:"config"` // JSON字符串，包含所有配置
	}

	if err := c.ShouldBindJSON(&settings); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code": -1,
			"msg":  "请求参数错误: " + err.Error(),
		})
		return
	}

	// 更新购物车项设置
	// 直接将整个config存储为JSON字符串
	updates := map[string]interface{}{
		"config": settings.Config,
	}

	if err := database.DB.Model(&cartItem).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code": -1,
			"msg":  "更新设置失败: " + err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"code": 0,
		"msg":  "设置更新成功",
		"data": cartItem,
	})
}
