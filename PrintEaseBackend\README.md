# PrintEaseBackend - 自助打印系统后端

## 项目介绍
PrintEaseBackend是自助打印项目PrintEase的后端服务，采用Go语言开发，基于Gin框架构建。该系统提供了用户管理、文件上传、打印配置、订单管理、商家管理等功能，支持微信小程序和QQ小程序前端。

## 技术栈
- 语言：Go 1.24.0
- Web框架：Gin 1.10.0
- ORM框架：GORM 1.25.12
- 数据库：MySQL
- WebSocket：gorilla/websocket
- 配置管理：yaml
- 文件处理：Ghostscript, LibreOffice
- 认证：JWT (JSON Web Token)

## 项目结构
```
PrintEaseBackend/
├── cmd/
│   └── main.go                 # 程序入口
├── internal/
│   ├── handlers/               # 处理函数
│   │   ├── cart_handler.go     # 购物车处理
│   │   ├── download.go         # 文件下载处理
│   │   ├── merchant_handler.go # 商家管理处理
│   │   ├── order_handler.go    # 订单处理
│   │   ├── store_handler.go    # 商店处理
│   │   ├── upload.go           # 文件上传处理
│   │   ├── user_handler.go     # 用户处理
│   │   └── websocket_handler.go # WebSocket处理
│   ├── models/                 # 数据模型  
│   │   ├── cart_item.go        # 购物车项模型
│   │   ├── file.go             # 文件模型
│   │   ├── merchant.go         # 商家模型
│   │   ├── order.go            # 订单模型
│   │   ├── store.go            # 商店模型
│   │   ├── user.go             # 用户模型
│   │   ├── user_auth.go        # 用户认证模型
│   │   ├── user_req.go         # 用户请求/响应模型
│   │   └── user_session.go     # 用户会话模型
│   ├── routes/                 # 路由配置
│   │   └── routes.go           # 路由定义
│   ├── config/                 # 配置文件
│   │   ├── config.go           # 配置结构定义
│   │   └── config.yaml         # 配置文件
│   ├── database/               # 数据库配置
│   │   ├── migrations/         # 数据库迁移脚本
│   │   │   └── order_tables.sql # 订单表结构
│   │   └── mysql.go            # MySQL连接配置
│   ├── middleware/             # 中间件
│   │   ├── auth.go             # JWT认证中间件
│   │   ├── cors.go             # 跨域中间件
│   │   └── logger.go           # 日志中间件
│   └── services/               # 业务逻辑层
│       ├── file_service.go     # 文件服务
│       ├── merchant_service.go # 商家服务
│       ├── order_service.go    # 订单服务
│       ├── store_service.go    # 商店服务
│       └── user_service.go     # 用户服务
├── uploads/                    # 上传文件存储目录
└── logs/                       # 日志文件目录
```

## 主要功能
1. **用户系统**
   - 用户注册和登录（支持多种登录方式）
   - 用户信息管理
   - 用户认证和授权
   - 会话管理

2. **文件管理**
   - 支持多种文件格式上传（PDF, Word, Excel, PPT, 图片等）
   - 文件预览和下载
   - 文件页数自动计算

3. **商家管理**
   - 商家注册和登录
   - 商家信息管理
   - 商家权限控制（店主/员工）

4. **商店管理**
   - 商店信息管理
   - 商店位置服务范围设置
   - 商店营业状态管理

5. **订单系统**
   - 购物车管理
   - 订单创建和支付
   - 订单状态跟踪
   - 订单历史查询

6. **打印代理通信**
   - WebSocket实时通信
   - 打印任务分发
   - 打印状态同步

## API接口
系统提供以下主要API接口：

### 用户相关
- `POST /api/users/login` - 用户登录
- `GET /api/users/info` - 获取用户信息
- `PUT /api/users/info` - 更新用户信息
- `POST /api/users/change-password` - 修改密码

### 文件相关
- `POST /api/upload` - 上传文件
- `GET /api/download` - 下载文件

### 购物车相关
- `GET /api/cart-items` - 获取购物车项目
- `PUT /api/cart-items/:id` - 更新购物车项目设置
- `PUT /api/cart-items/:id/status` - 更新购物车项目状态
- `PUT /api/cart-items/batch-status` - 批量更新购物车状态

### 订单相关
- `POST /api/orders` - 创建订单
- `GET /api/orders` - 获取订单列表
- `GET /api/orders/:id` - 获取订单详情
- `PUT /api/orders/:id/status` - 更新订单状态

### 商家相关
- `POST /api/merchants/login` - 商家登录
- `POST /api/merchants` - 创建商家
- `PUT /api/merchants/:id` - 更新商家
- `GET /api/merchants/:id` - 获取商家信息
- `GET /api/merchants` - 获取商家列表
- `DELETE /api/merchants/:id` - 删除商家
- `POST /api/merchants/change-password` - 修改密码

### 商店相关
- `GET /api/stores` - 获取商店列表

### WebSocket
- `GET /ws/print-agent` - 打印代理WebSocket连接

## 认证系统
### 1. JWT认证
#### 1.1 Token结构
- Header: 使用HS256算法
- Payload包含:
  - user_id: 用户ID（用户登录时）
  - merchant_id: 商家ID（商家登录时）
  - store_id: 店铺ID
  - username: 用户名
  - role: 角色类型（user/merchant_owner/merchant_staff）
  - client_type: 客户端类型（wx_mini/qq_mini/web/agent）
  - exp: 过期时间
  - iat: 签发时间

#### 1.2 认证流程
1. 用户登录认证：
   - 路径：POST /api/users/login
   - 请求体：
     ```json
     {
       "identity_type": "string",  // wx_mini/qq_mini/web/client
       "identifier": "string",     // openid/用户名/手机号/邮箱
       "credential": "string",     // 密码/code（可选）
       "client_type": "string",    // wx_mini/qq_mini/web/client
       "device_info": "string"     // 设备信息（可选）
     }
     ```
   - 响应体：
     ```json
     {
       "code": 0,
       "msg": "success",
       "data": {
         "token": "string",
         "user": {
           "id": "int",
           "nickname": "string",
           "avatar_url": "string",
           "phone": "string",
           "email": "string",
           "address": "string",
           "balance": "float"
         },
         "expires_in": "int"  // token过期时间（秒）
       }
     }
     ```

2. API认证：
   - 在请求头中添加：
     ```
     Authorization: Bearer <token>
     ```
   - 中间件自动验证token有效性
   - 支持token过期检查和自动续期

### 2. 权限控制
#### 2.1 角色类型权限
- 普通用户（user）：
  - 基本的用户功能
  - 文件上传下载
  - 订单管理
  - 个人信息管理

- 店主（merchant_owner）：
  - 完全的店铺管理权限
  - 员工管理权限
  - 订单管理权限
  - 打印管理权限

- 员工（merchant_staff）：
  - 基本的订单处理权限
  - 打印管理权限
  - 有限的店铺信息查看权限

#### 2.2 客户端类型权限
- 小程序客户端（wx_mini/qq_mini）：
  - 用于普通用户
  - 基本的用户功能

- Web客户端：
  - 用于商家管理后台
  - 完整的管理功能

- Agent客户端：
  - 用于打印代理程序
  - 仅打印相关功能
  - WebSocket连接权限

### 3. 安全特性
#### 3.1 Token安全
- Token有效期：
  - 小程序端：7天
  - Web端：2小时
  - Agent端：30天
- Token自动续期
- Token黑名单机制

#### 3.2 密码安全
- 密码加密存储（bcrypt）
- 密码强度要求
- 登录失败次数限制

#### 3.3 通信安全
- HTTPS支持
- WSS支持
- CORS配置
- XSS防护
- CSRF防护

### 4. 异常处理
#### 4.1 认证异常
- Token过期
- Token无效
- 权限不足
- 登录失败

#### 4.2 WebSocket异常
- 连接断开
- 认证失败
- 心跳超时

## 部署指南

### 环境要求
- Go 1.24.0 或更高版本
- MySQL 5.7 或更高版本
- Ghostscript（用于PDF处理）
- LibreOffice（用于Office文档处理）
- ImageMagick（用于图片处理）

### 配置说明
配置文件支持开发环境和生产环境分离，分别使用 `internal/config/config.dev.yaml` 和 `internal/config/config.prod.yaml`。使用 viper 进行配置管理，可通过环境变量 `APP_ENV` 切换配置文件：

```bash
# 开发环境（默认）
APP_ENV=dev ./printease-backend

# 生产环境
APP_ENV=prod ./printease-backend
```

配置文件示例（以开发环境为例）：

```yaml
server:
  host: "127.0.0.1"    # 开发环境使用本地地址
  port: 8080           # 服务端口
  mode: "debug"        # gin运行模式：debug/release
  timeout: 30          # 请求超时时间（秒）

database:
  driver: mysql
  host: localhost
  port: 3306
  username: root
  password: ******
  dbname: print_ease
  charset: utf8mb4
  parse_time: true
  loc: Local
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

auth:
  jwt_secret: "your_jwt_secret"
  token_expire:
    wx_mini: 604800    # 7天
    qq_mini: 604800    # 7天
    web: 7200          # 2小时
    agent: 2592000     # 30天

store:
  maxDistance: 20.0  # 最大服务范围,单位:公里 
  
upload:
  uploadDir: "uploads"
  maxFileSize: 32  # 32MB
```

生产环境配置（config.prod.yaml）中的敏感信息（如数据库密码）建议通过环境变量注入：

```bash
# 通过环境变量覆盖数据库密码
export DB_PASSWORD=your_password
APP_ENV=prod ./printease-backend
```

### 启动步骤
1. 克隆代码库
2. 配置数据库和其他设置
3. 执行数据库迁移脚本
4. 编译并运行
   ```bash
   go build -o printease-backend ./cmd
   ./printease-backend
   ```

## 开发计划
1. 增加支付接口集成
2. 优化文件处理性能
3. 增加更多打印配置选项
4. 实现商家端管理界面
5. 增加用户积分系统
6. 实现打印机管理功能

## 注意事项
- 确保上传目录有足够的磁盘空间和写入权限
- 配置适当的文件大小限制，避免服务器资源耗尽
- 定期清理临时文件和过期文件
- 配置数据库备份策略
- 注意保护用户敏感信息
- 定期更新安全补丁
