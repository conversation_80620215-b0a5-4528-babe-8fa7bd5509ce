/**
 * 根据文件类型获取对应的图标
 * @param {string} fileType - 文件类型
 * @return {string} - 图标路径
 */
export function getFileIcon(fileType) {
    const iconMap = {
        'doc': '/static/icons/word.png',
        'docx': '/static/icons/word.png',
        'xls': '/static/icons/excel.png',	
        'xlsx': '/static/icons/excel.png',
        'ppt': '/static/icons/ppt.png',
        'pptx': '/static/icons/ppt.png',
        'pdf': '/static/icons/pdf.png',
        'txt': '/static/icons/txt.png',
        'rtf': '/static/icons/txt.png',
        
        'jpg': '/static/icons/image.png',
        'jpeg': '/static/icons/image.png',
        'png': '/static/icons/image.png',
        'gif': '/static/icons/image.png',
        'bmp': '/static/icons/image.png',
        'tif': '/static/icons/image.png',
        'tiff': '/static/icons/image.png'
    };
    
    // 简单处理文件类型
    const type = fileType ? fileType.toLowerCase().split('/').pop() : '';
    return iconMap[type] || '/static/icons/default.png';
}
