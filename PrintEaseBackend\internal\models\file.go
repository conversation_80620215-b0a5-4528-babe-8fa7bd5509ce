package models

import (
	"errors"
	"path/filepath"
	"strings"
	"time"
)

// 文件状态常量
const (
	FileStatusNormal   int8 = 0 // 正常状态
	FileStatusDeleted  int8 = 1 // 已删除
	FileStatusDisabled int8 = 2 // 已禁用
)

// File 文件模型
type File struct {
	ID        uint64    `gorm:"primaryKey;column:id" json:"id"`
	UserID    uint64    `gorm:"not null;column:user_id" json:"user_id"`        // 用户ID
	Filename  string    `gorm:"not null;column:filename" json:"filename"`      // 文件名
	FilePath  string    `gorm:"not null;column:file_path" json:"file_path"`    // 存储路径
	FileSize  int64     `gorm:"not null;column:file_size" json:"file_size"`    // 文件大小
	FileType  string    `gorm:"not null;column:file_type" json:"file_type"`    // 文件类型
	PageCount int       `gorm:"default:1;column:page_count" json:"page_count"` // 文件页数
	Status    int8      `gorm:"default:0;column:status" json:"status"`         // 状态
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`
	User      User      `gorm:"foreignKey:UserID" json:"-"` // 关联用户
}

// 文件类型常量
const (
	FileTypePDF  = "pdf"
	FileTypeDOC  = "doc"
	FileTypeDOCX = "docx"
	FileTypeXLS  = "xls"
	FileTypeXLSX = "xlsx"
	FileTypePPT  = "ppt"
	FileTypePPTX = "pptx"
	FileTypeTXT  = "txt"
	FileTypeRTF  = "rtf"
	FileTypeJPG  = "jpg"
	FileTypeJPEG = "jpeg"
	FileTypePNG  = "png"
	FileTypeGIF  = "gif"
	FileTypeBMP  = "bmp"
	FileTypeTIF  = "tif"
	FileTypeTIFF = "tiff"
)

// 允许的文件类型映射
var AllowedFileTypes = map[string]bool{
	FileTypePDF:  true,
	FileTypeDOC:  true,
	FileTypeDOCX: true,
	FileTypeXLS:  true,
	FileTypeXLSX: true,
	FileTypePPT:  true,
	FileTypePPTX: true,
	FileTypeTXT:  true,
	FileTypeRTF:  true,
	FileTypeJPG:  true,
	FileTypeJPEG: true,
	FileTypePNG:  true,
	FileTypeGIF:  true,
	FileTypeBMP:  true,
	FileTypeTIF:  true,
	FileTypeTIFF: true,
}

// ValidateFileType 验证文件类型是否允许
func ValidateFileType(filename string) error {
	ext := strings.ToLower(filepath.Ext(filename))
	if ext == "" {
		return errors.New("文件没有扩展名")
	}

	// 去掉扩展名前面的点
	ext = ext[1:]
	if !AllowedFileTypes[ext] {
		return errors.New("不支持的文件类型")
	}
	return nil
}

// TableName 指定表名
func (File) TableName() string {
	return "files"
}
