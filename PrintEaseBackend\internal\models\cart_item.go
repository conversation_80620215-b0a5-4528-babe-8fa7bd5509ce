package models

import (
	"PrintEaseBackend/internal/database"
	"time"
)

// CartItem 购物车模型
type CartItem struct {
	ID        uint64    `gorm:"primaryKey;column:id" json:"id"`
	UserID    uint64    `gorm:"not null;column:user_id" json:"user_id"` // 用户ID
	FileID    uint64    `gorm:"not null;column:file_id" json:"file_id"` // 文件ID
	Config    string    `gorm:"type:json;column:config" json:"config"`  // 打印配置
	Status    int8      `gorm:"default:0;column:status" json:"status"`  // 状态
	CreatedAt time.Time `gorm:"column:created_at" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at" json:"updated_at"`
	User      User      `gorm:"foreignKey:UserID" json:"-"`    // 关联用户
	File      File      `gorm:"foreignKey:FileID" json:"file"` // 关联文件
}

// 购物车项目状态常量
const (
	CartStatusPending   int8 = 0 // 待处理/未打印
	CartStatusPrinting  int8 = 1 // 打印中
	CartStatusCompleted int8 = 2 // 已完成打印
	CartStatusCancelled int8 = 3 // 已取消/已删除
)

// CreateCartItem 创建购物车项
func CreateCartItem(userID, fileID uint64) (*CartItem, error) {
	item := &CartItem{
		UserID: userID,
		FileID: fileID,
		Status: CartStatusPending,
		Config: "{}", // 初始化为空JSON对象
	}

	if err := database.DB.Create(item).Error; err != nil {
		return nil, err
	}
	return item, nil
}

// TableName 指定表名
func (CartItem) TableName() string {
	return "cart_items"
}
