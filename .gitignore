# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# 日志文件
*.log
logs/

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# PyCharm
.idea/
*.iml
*.iws
.idea_modules/

# VS Code
.vscode/
*.code-workspace

# 项目特定文件
config.json
uploads/
temp/
*.db
*.sqlite3

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 虚拟环境
venv/
env/
ENV/

# 其他
*.bak
*.swp
*.swo
*~ 