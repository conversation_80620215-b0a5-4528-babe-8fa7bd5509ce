package models

// UserLoginReq 用户登录请求
type UserLoginReq struct {
	IdentityType string `json:"identity_type" binding:"required"` // 登录类型：wx_mini/qq_mini/web/client
	Identifier   string `json:"identifier" binding:"required"`    // 标识（openid/用户名/手机号/邮箱）
	Credential   string `json:"credential"`                       // 凭证（密码/code）
	ClientType   string `json:"client_type" binding:"required"`   // 客户端类型
	DeviceInfo   string `json:"device_info"`                      // 设备信息
}

// UserLoginResp 用户登录响应
type UserLoginResp struct {
	Token     string `json:"token"`      // 访问令牌
	User      *User  `json:"user"`       // 用户信息
	ExpiresIn int64  `json:"expires_in"` // 过期时间（秒）
}

// UserUpdateReq 用户信息更新请求
type UserUpdateReq struct {
	Nickname  *string `json:"nickname"`   // 用户昵称
	AvatarURL *string `json:"avatar_url"` // 头像URL
	Phone     *string `json:"phone"`      // 手机号
	Email     *string `json:"email"`      // 邮箱
	Address   *string `json:"address"`    // 地址
}

// UserChangePasswordReq 修改密码请求
type UserChangePasswordReq struct {
	OldPassword string `json:"old_password" binding:"required"` // 旧密码
	NewPassword string `json:"new_password" binding:"required"` // 新密码
}
