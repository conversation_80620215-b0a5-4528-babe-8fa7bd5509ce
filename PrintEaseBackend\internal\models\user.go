package models

import "time"

// User 用户模型
type User struct {
	ID            int64      `gorm:"primarykey" json:"id"`
	Username      string     `gorm:"size:50;unique" json:"username"`                 // 用户名（网页/客户端登录用）
	Password      string     `gorm:"size:255" json:"-"`                              // 密码（网页/客户端登录用）
	Nickname      string     `gorm:"size:50" json:"nickname"`                        // 用户昵称
	AvatarURL     string     `gorm:"size:255" json:"avatar_url"`                     // 头像URL
	Phone         string     `gorm:"size:20" json:"phone"`                           // 手机号
	Email         string     `gorm:"size:100" json:"email"`                          // 邮箱
	Address       string     `gorm:"size:255" json:"address"`                        // 地址
	Balance       float64    `gorm:"type:decimal(10,2);default:0.00" json:"balance"` // 账户余额
	Status        int8       `gorm:"default:1" json:"status"`                        // 状态：0-禁用 1-正常
	LastLoginTime *time.Time `json:"last_login_time"`                                // 最后登录时间
	LastLoginIP   string     `gorm:"size:50" json:"last_login_ip"`                   // 最后登录IP
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

func (User) TableName() string {
	return "users"
}
