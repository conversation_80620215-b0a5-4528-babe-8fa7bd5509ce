# Go语言相关忽略
# 编译生成的二进制文件
*.exe
*.exe~
*.dll
*.so
*.dylib

# 测试二进制文件
*.test

# 覆盖率工具生成的文件
*.out
*.prof
*.cov

# Go工具链生成的文件
/go.work

# 依赖目录
/vendor/

# IDE相关忽略
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 项目特定忽略
/uploads/*
!/uploads/.gitkeep
/logs/*
!/logs/.gitkeep

# 配置文件（可能包含敏感信息）
# 注意：如果配置文件需要版本控制，请移除下面的行
# /internal/config/config.yaml

# 编译输出
/bin/
/dist/
/build/
PrintEaseBackend
printease-backend

# 临时文件
/tmp/
*.tmp
*~

# 环境变量文件
.env
.env.local

# 调试文件
__debug_bin
debug

# 系统文件
Thumbs.db 