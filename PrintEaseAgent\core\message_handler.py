import os
import json
import logging
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, Any
from .database import PrintTaskManager

logger = logging.getLogger(__name__)

class MessageHandler(QObject):
    """处理从WebSocket接收的消息"""
    
    # 定义信号
    print_task_received = pyqtSignal(dict)  # 接收到打印任务
    auth_response = pyqtSignal(bool, str)   # 认证响应
    error_occurred = pyqtSignal(str)        # 发生错误
    
    def __init__(self, file_downloader, uploads_dir="uploads", task_processor=None):
        """初始化消息处理器
        
        Args:
            file_downloader: 文件下载器实例
            uploads_dir (str): 上传文件保存目录
            task_processor: 任务处理器实例
        """
        super().__init__()
        self.file_downloader = file_downloader
        self.uploads_dir = uploads_dir
        self.task_processor = task_processor
        self.task_manager = PrintTaskManager()
        
        # 确保上传目录存在
        os.makedirs(self.uploads_dir, exist_ok=True)
        
    def handle_message(self, message):
        """处理接收到的消息
        
        Args:
            message (dict): 接收到的消息
        """
        try:
            message_type = message.get('type')
            
            if message_type == 'auth_response':
                self._handle_auth_response(message)
            elif message_type == 'print_task':
                self._handle_print_task(message)
            elif message_type == 'heartbeat':
                # 心跳消息不需要特殊处理
                pass
            else:
                logger.warning(f"收到未知类型的消息: {message_type}")
                
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            logger.error(error_msg)
            self.error_occurred.emit(error_msg)
    
    def _handle_auth_response(self, message):
        """处理认证响应消息
        
        Args:
            message (dict): 认证响应消息
        """
        success = message.get('success', False)
        msg = message.get('message', '')
        
        if success:
            logger.info("认证成功")
        else:
            logger.error(f"认证失败: {msg}")
            
        self.auth_response.emit(success, msg)
    
    def _handle_print_task(self, message):
        """处理打印任务消息"""
        try:
            # 获取数据部分
            data = message.get('data', {})
            if not data:
                raise ValueError("消息缺少data字段")

            # 验证必填字段
            required_fields = [
                'order_id', 'order_item_id', 'file_info', 'print_config'
            ]
            
            for field in required_fields:
                if field not in data:
                    raise ValueError(f"缺少必填字段: {field}")
            
            file_info = data['file_info']
            required_file_fields = ['file_id', 'filename', 'file_type', 'file_size', 'page_count', 'download_url']
            for field in required_file_fields:
                if field not in file_info:
                    raise ValueError(f"文件信息缺少必填字段: {field}")
                    
            # 验证print_config
            if not isinstance(data['print_config'], dict):
                raise ValueError("print_config必须是一个字典")
                
            # 生成task_id
            task_id = f"task_{data['order_id']}_{data['order_item_id']}"
            
            # 准备任务数据
            task_data = {
                'task_id': task_id,
                'order_id': data['order_id'],
                'order_item_id': data['order_item_id'],
                'file_id': file_info['file_id'],
                'filename': file_info['filename'],
                'file_type': file_info['file_type'],
                'file_size': file_info['file_size'],
                'page_count': file_info['page_count'],
                'download_url': file_info['download_url'],
                'print_config': json.dumps(data['print_config'])
            }
            
            # 存储任务到数据库
            self.task_manager.add_task(**task_data)
            
            logger.info(f"成功接收并存储打印任务: {task_id}")
            
            # 打印任务信息
            self._print_task_info(task_id, file_info, data['print_config'])
            
            # 下载文件
            success = self._download_file(task_id, file_info['download_url'], file_info['filename'])
            
            # 将任务添加到处理队列
            if success and self.task_processor:
                self.task_processor.add_task(task_id)
                logger.info(f"已将任务 {task_id} 添加到处理队列")
            elif not self.task_processor:
                logger.warning(f"无法将任务 {task_id} 添加到处理队列: task_processor不存在")
            
            # 发送任务接收信号
            self.print_task_received.emit({"task_id": task_id})
        
        except ValueError as e:
            logger.error(f"打印任务数据验证失败: {str(e)}")
            raise
            
        except Exception as e:
            logger.error(f"处理打印任务时发生错误: {str(e)}")
            raise
    
    def _print_task_info(self, task_id, file_info, print_config):
        """将打印任务信息打印到控制台
        
        Args:
            task_id (str): 任务ID
            file_info (dict): 文件信息
            print_config (dict): 打印配置
        """
        print("\n" + "="*50)
        print(f"收到打印任务: {task_id}")
        print("-"*50)
        
        print("文件信息:")
        print(f"  文件ID: {file_info.get('file_id')}")
        print(f"  文件名: {file_info.get('filename')}")
        print(f"  文件类型: {file_info.get('file_type')}")
        print(f"  文件大小: {file_info.get('file_size')} 字节")
        
        print("\n打印配置:")
        for key, value in print_config.items():
            print(f"  {key}: {value}")
            
        print("="*50 + "\n")
    
    def _download_file(self, task_id, download_url, filename):
        """下载文件并保存到本地
        
        Args:
            task_id (str): 任务ID
            download_url (str): 文件下载URL
            filename (str): 文件名
        """
        try:
            # 构建保存路径
            save_path = os.path.join(self.uploads_dir, filename)
            
            # 确保文件名不重复
            base_name, ext = os.path.splitext(filename)
            counter = 1
            while os.path.exists(save_path):
                save_path = os.path.join(self.uploads_dir, f"{base_name}_{counter}{ext}")
                counter += 1
            
            # 下载文件
            logger.info(f"开始下载文件: {download_url} -> {save_path}")
            success = self.file_downloader.download_file(download_url, save_path)
            
            if success:
                logger.info(f"文件下载成功: {save_path}")
                print(f"文件已保存到: {save_path}")
            else:
                logger.error(f"文件下载失败: {download_url}")
                
        except Exception as e:
            logger.error(f"下载文件时出错: {str(e)}")
            raise 

