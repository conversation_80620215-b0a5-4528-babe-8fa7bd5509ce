package services

import (
	"PrintEaseBackend/internal/database"
	"PrintEaseBackend/internal/models"
	"errors"
	"fmt"

	"gorm.io/gorm"
)

// OrderService 订单服务
type OrderService struct{}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	UserID  uint64   `json:"user_id"`
	StoreID uint64   `json:"store_id"`
	CartIDs []uint64 `json:"cart_ids"`
	Remark  string   `json:"remark"`
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(req *CreateOrderRequest) (*models.Order, error) {
	// 参数校验
	if req.UserID == 0 {
		return nil, errors.New("用户ID不能为空")
	}
	if req.StoreID == 0 {
		return nil, errors.New("商店ID不能为空")
	}
	if len(req.CartIDs) == 0 {
		return nil, errors.New("购物车项不能为空")
	}

	// 开启事务
	tx := database.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 查询购物车项
	var cartItems []models.CartItem
	if err := tx.Where("id IN ?", req.CartIDs).Preload("File").Find(&cartItems).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("查询购物车项失败: %w", err)
	}

	// 检查购物车项是否都属于当前用户
	for _, item := range cartItems {
		if item.UserID != req.UserID {
			tx.Rollback()
			return nil, errors.New("购物车项不属于当前用户")
		}
	}

	// 创建订单
	var totalFee float64 = 0
	order := &models.Order{
		UserID:   req.UserID,
		StoreID:  req.StoreID,
		TotalFee: totalFee, // 暂时设为0，后面计算
		Status:   models.OrderStatusPending,
		Remark:   req.Remark,
	}

	if err := tx.Create(order).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建订单失败: %w", err)
	}

	// 创建订单项
	for _, item := range cartItems {
		// 这里可以根据打印配置计算价格
		itemPrice := s.calculateItemPrice(item)
		totalFee += itemPrice

		orderItem := models.OrderItem{
			OrderID:    order.ID,
			CartItemID: item.ID,
			Price:      itemPrice,
			Status:     models.OrderStatusPending,
		}

		if err := tx.Create(&orderItem).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("创建订单项失败: %w", err)
		}

		// 更新购物车项状态
		if err := tx.Model(&item).Update("status", models.CartStatusPrinting).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("更新购物车项状态失败: %w", err)
		}
	}

	// 更新订单总价
	if err := tx.Model(order).Update("total_fee", totalFee).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("更新订单总价失败: %w", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %w", err)
	}

	// 查询完整订单信息
	var completeOrder models.Order
	if err := database.DB.Preload("Store").Preload("OrderItems.CartItem.File").First(&completeOrder, order.ID).Error; err != nil {
		return nil, fmt.Errorf("查询订单详情失败: %w", err)
	}

	return &completeOrder, nil
}

// calculateItemPrice 计算购物车项的价格
// 这是一个简单的价格计算函数，实际应用中可能需要更复杂的逻辑
func (s *OrderService) calculateItemPrice(item models.CartItem) float64 {
	// 这里可以根据打印配置(如页数、纸张类型、彩色或黑白等)计算价格
	// 简单示例: 假设每页0.5元
	basePrice := 0.5
	return float64(item.File.PageCount) * basePrice
}

// GetOrderByID 获取订单详情
func (s *OrderService) GetOrderByID(orderID, userID uint64) (*models.Order, error) {
	var order models.Order
	err := database.DB.Where("id = ? AND user_id = ?", orderID, userID).
		Preload("Store").
		Preload("OrderItems.CartItem.File").
		First(&order).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("订单不存在")
		}
		return nil, fmt.Errorf("查询订单失败: %w", err)
	}

	return &order, nil
}

// GetOrderList 获取订单列表
func (s *OrderService) GetOrderList(userID uint64, status *int8, page, pageSize int) ([]models.Order, int64, error) {
	var orders []models.Order
	var total int64

	// 构建查询
	query := database.DB.Model(&models.Order{}).Where("user_id = ?", userID)

	// 添加状态过滤
	if status != nil {
		query = query.Where("status = ?", *status)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 添加预加载
	query = query.Preload("Store")
	// 修改这里：添加OrderItems及其关联数据的预加载
	query = query.Preload("OrderItems")
	query = query.Preload("OrderItems.CartItem")
	query = query.Preload("OrderItems.CartItem.File")

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).
		Order("created_at DESC").
		Find(&orders).Error; err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}

// UpdateOrderStatus 更新订单状态
func (s *OrderService) UpdateOrderStatus(orderID, userID uint64, status int8) error {
	// 先检查订单是否属于当前用户
	var count int64
	if err := database.DB.Model(&models.Order{}).Where("id = ? AND user_id = ?", orderID, userID).Count(&count).Error; err != nil {
		return fmt.Errorf("查询订单失败: %w", err)
	}
	if count == 0 {
		return errors.New("订单不存在或不属于当前用户")
	}

	// 更新订单状态
	if err := database.DB.Model(&models.Order{}).Where("id = ?", orderID).Update("status", status).Error; err != nil {
		return fmt.Errorf("更新订单状态失败: %w", err)
	}

	// 如果是取消订单，则更新关联的购物车项状态
	if status == models.OrderStatusCancelled {
		if err := s.updateOrderItemsStatus(orderID, models.OrderStatusCancelled); err != nil {
			return err
		}
	}

	return nil
}

// updateOrderItemsStatus 更新订单项状态
func (s *OrderService) updateOrderItemsStatus(orderID uint64, status int8) error {
	// 更新订单项状态
	if err := database.DB.Model(&models.OrderItem{}).Where("order_id = ?", orderID).Update("status", status).Error; err != nil {
		return fmt.Errorf("更新订单项状态失败: %w", err)
	}

	// 如果是取消订单，则恢复购物车项状态为待处理
	if status == models.OrderStatusCancelled {
		var orderItems []models.OrderItem
		if err := database.DB.Where("order_id = ?", orderID).Find(&orderItems).Error; err != nil {
			return fmt.Errorf("查询订单项失败: %w", err)
		}

		for _, item := range orderItems {
			if err := database.DB.Model(&models.CartItem{}).Where("id = ?", item.CartItemID).Update("status", models.CartStatusPending).Error; err != nil {
				return fmt.Errorf("恢复购物车项状态失败: %w", err)
			}
		}
	}

	return nil
}
