package database

import (
	"PrintEaseBackend/internal/config"
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// InitDB 初始化数据库连接
func InitDB(conf *config.DatabaseConfig) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		conf.Username,
		conf.Password,
		conf.Host,
		conf.Port,
		conf.DBName,
		conf.Charset,
		conf.ParseTime,
		conf.Loc,
	)

	// 配置GORM
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info), // 开发环境下设置为 Info，生产环境建议设置为 Silent
	}

	// 建立连接
	db, err := gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 获取通用数据库对象 sql.DB，然后使用其提供的功能
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取底层数据库连接失败: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(conf.MaxIdleConns)                                    // 设置空闲连接池中的最大连接数
	sqlDB.SetMaxOpenConns(conf.MaxOpenConns)                                    // 设置打开数据库连接的最大数量
	sqlDB.SetConnMaxLifetime(time.Duration(conf.ConnMaxLifetime) * time.Second) // 设置连接可复用的最大时间

	// 测试连接
	err = sqlDB.Ping()
	if err != nil {
		return fmt.Errorf("测试数据库连接失败: %v", err)
	}

	DB = db
	return nil
}
