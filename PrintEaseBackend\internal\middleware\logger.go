package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// 自定义响应写入器，用于捕获响应内容
type responseBodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

// 重写Write方法，同时写入到原始响应和缓冲区
func (r responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

// 重写WriteString方法，同时写入到原始响应和缓冲区
func (r responseBodyWriter) WriteString(s string) (int, error) {
	r.body.WriteString(s)
	return r.ResponseWriter.WriteString(s)
}

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 开始时间
		startTime := time.Now()

		// 创建日志目录
		logDir := "logs"
		if _, err := os.Stat(logDir); os.IsNotExist(err) {
			os.Mkdir(logDir, 0755)
		}

		// 日志文件名（按日期）
		today := time.Now().Format("2006-01-02")
		logFile := filepath.Join(logDir, fmt.Sprintf("access_%s.log", today))

		// 打开日志文件
		file, err := os.OpenFile(logFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			log.Println("无法打开日志文件:", err)
			return
		}
		defer file.Close()

		// 记录服务启动信息
		if c.Request.URL.Path == "/" && c.Request.Method == "GET" {
			currentTime := time.Now().Format("2006-01-02 15:04:05")
			fmt.Fprintf(file, "%s 服务启动 - 时间: %s\n", time.Now().Format("2006/01/02 15:04:05"), currentTime)
		}

		// 包装响应写入器以捕获响应内容
		responseBody := &bytes.Buffer{}
		wrappedWriter := &responseBodyWriter{
			ResponseWriter: c.Writer,
			body:           responseBody,
		}
		c.Writer = wrappedWriter

		// 处理请求
		c.Next()

		// 访问结束时间
		endTime := time.Now()
		// 执行时间
		latencyTime := endTime.Sub(startTime)

		// 获取请求体
		var requestBody interface{}
		if c.Request.Body != nil && c.Request.ContentLength > 0 {
			bodyBytes, _ := io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes)) // 重置请求体，以便后续处理

			contentType := c.GetHeader("Content-Type")
			if strings.Contains(contentType, "application/json") {
				json.Unmarshal(bodyBytes, &requestBody)
			} else if strings.Contains(contentType, "multipart/form-data") {
				// 解析文件上传信息
				err := c.Request.ParseMultipartForm(32 << 20) // 32MB max memory
				if err == nil {
					requestBody = map[string]interface{}{
						"form_data": c.Request.MultipartForm.Value,
						"files": map[string]interface{}{
							"count":      len(c.Request.MultipartForm.File),
							"file_names": getFileNames(c.Request.MultipartForm.File),
						},
					}
				} else {
					requestBody = "无法解析multipart/form-data请求体"
				}
			} else {
				requestBody = fmt.Sprintf("非JSON格式请求体 (Content-Type: %s)", contentType)
			}
		}

		// 获取查询参数
		queryParams := c.Request.URL.RawQuery

		// 获取用户代理
		//userAgent := c.Request.UserAgent()

		// 获取客户端IP
		clientIP := c.ClientIP()

		// 获取令牌（如果有）

		// 获取错误信息（如果有）
		errorMsg := c.Errors.String()

		// 处理响应内容
		var responseData interface{}
		contentLength := wrappedWriter.body.Len()

		// 根据内容类型和大小决定如何处理响应内容
		if contentLength > 0 {
			contentType := wrappedWriter.Header().Get("Content-Type")

			// 对于JSON响应，解析为结构化数据
			if strings.Contains(contentType, "application/json") {
				json.Unmarshal(wrappedWriter.body.Bytes(), &responseData)
			} else if contentLength > 1024 {
				// 对于大型非JSON响应，只记录内容类型和大小
				responseData = fmt.Sprintf("二进制响应 (%s, %d 字节)", contentType, contentLength)
			} else {
				// 对于小型非JSON响应，记录全部内容
				responseData = wrappedWriter.body.String()
			}
		}

		// 组装日志信息
		logMap := map[string]interface{}{
			//"timestamp": endTime.Format("2006-01-02 15:04:05"),
			"client_ip":    clientIP,
			"method":       c.Request.Method,
			"path":         c.Request.URL.Path,
			"query_params": queryParams,
			"status_code":  c.Writer.Status(),
			"latency":      latencyTime.String(),
			//"user_agent":    userAgent,
			"error":         errorMsg,
			"request_body":  requestBody,
			"response_body": responseData,
			/*"headers": map[string]string{
				"Content-Type":   c.GetHeader("Content-Type"),
				"Content-Length": c.GetHeader("Content-Length"),
				"Authorization":  token,
			},*/
		}

		// 转换为JSON
		logJSON, _ := json.Marshal(logMap)

		// 写入日志文件
		fmt.Fprintf(file, "%s %s\n", time.Now().Format("2006/01/02 15:04:05"), logJSON)
	}
}

// 掩码处理令牌
func maskToken(token string) string {
	if len(token) > 10 {
		return token[:10] + "****"
	}
	return token
}

// 获取上传文件名列表
func getFileNames(files map[string][]*multipart.FileHeader) []string {
	var fileNames []string
	for _, headers := range files {
		for _, header := range headers {
			fileNames = append(fileNames, header.Filename)
		}
	}
	return fileNames
}
