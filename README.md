# 项目介绍
    这是一个自助打印项目PrintEase，用户可以自助打印文件，并进行支付。

# 项目结构
    1. 自助打印小程序PrintEaseMiniApp:
        采用Uniapp开发，适配微信小程序和QQ小程序。
        该小程序代码放在/PrintEaseMiniApp目录下。

    2. 自助打印后台PrintEaseBackend:
        采用Go语言开发，Gin框架开发。
        该系统代码放在/PrintEaseBackend目录下。

    3. 自助打印代理程序PrintEaseAgent：
        采用Python语言开发，PyQt5框架开发图形界面。
        该系统代码放在/PrintEaseAgent目录下。
        由于后台无法直接与处于局域网内的打印机进行通信，因此需要一个代理程序，该代理程序负责将后台接收到的打印请求转发给打印机。代理程序安装在与打印店内打印机同一局域网的电脑上，用于管理打印机，以及转发后台接收到的打印请求给打印机。
        python3.10.11

# 项目目标
    自助打印项目PrintEase，设计场景为在1000个打印店商家，打印高峰期（期中期末毕业答辩时）同时10000个用户使用小程序时，系统稳定运行且响应快速。
    1. 自助打印小程序PrintEaseMiniApp，界面美观，操作简单且流畅，用户体验良好。
    2. 自助打印后台PrintEaseBackend，系统稳定，鲁棒性强，响应快速。

# 项目功能
    1. 自助打印小程序PrintEaseMiniApp。
        1.1 用户可以选择打印店商家。
            1.1.1 商家信息包括：商家名称，商家地址，商家电话，运营时间，商家公告。
        1.2 用户可以选择打印文件。
            1.2.1 打印类型包括，文件打印，图片打印，照片冲印，证件复印。
                1.2.1.1 文件打印：
                    支持文件类型：word，excel，ppt，pdf，txt。
                    支持打印预览（为了尽可能防止用户打印设置错误，打印预览应根据设置参数显示，而不是仅仅显示原文件）。
                    支持文件上传方式：微信聊天文件，本地文件。（后续添加百度网盘，金山文档，腾讯文档，压缩包，云文库。）
                1.2.1.2 图片打印：
                    将图片打印在普通纸张上。
                    支持设置参数：同文件打印。
                    支持打印预览：同文件打印。
                    支持图片上传方式：微信聊天图片，本地相册，拍照。
                1.2.1.3 照片冲印：
                    后续补充。
                1.2.1.4 证件复印：
                    后续补充。
            1.2.2 取件方式支持：店内打印，远程自取，商家配送（需要商家后台设置是否支持商家配送）。
                1.2.2.1 商家配送：
                    需要商家后台配置配送范围，配送费用。
                    需要用户填写配送时间，配送地址，联系电话。
                1.2.2.2 远程自取和商家配送需要商家设置是否需要订单页，默认需要。
            1.2.3 支持计算打印费用。
                1.2.3.1 需要商家后台设置打印价格表。
                1.2.3.2 需要在微信小程序显示打印价格表。
                1.2.3.3 需要根据打印类型，打印参数，打印数量，打印纸张等计算打印费用（参考打印设置参数）。
                1.2.3.4 需要根据会员，优惠劵等计算打印费用。
            1.2.4 支持查看历史订单。
            1.2.5 支持会员，优惠劵等。
            1.2.6 支持退款。
                如果文件未打印，可直接退款。
                如果文件已打印，退款需商家同意。
            1.2.7 支持用户评价。

    2. 自助打印后台PrintEaseBackend。
        2.1 支持商家管理。
            2.1.1 支持商家信息管理。
            2.1.2 支持商家公告管理。
            2.1.3 支持商家打印价格表管理。
            2.1.4 支持商家配送管理（是否支持商家配送，配送范围，配送费用）。
            2.1.5 支持商家订单页管理（是否需要订单页）。
        2.2 后续补充。       
        
# 项目开发计划
技术方案：
1. （小程序端）文件缓存策略。
    1.1 需求分析
        - 在打印文件前，用户可能多次预览文件，所以要在小程序端缓存文件。
    1.2 技术方案
        - 在用户选择并上传文件到后台的同时，在小程序端缓存该文件。
        - 当用户预览文件时，检查缓存中是否有该文件，如果有，直接从缓存获取该文件， 如果没有，向后台请求该文件数据。

2. 文件命名策略。
    1.1 需求分析
        - 用户在小程序端页面，以及打印出来的文件，需显示文件原始名称，所以需要存储文件原始名称。
        - 由于小程序缓存文件的名称为系统随机生成的文件名，为了能在缓存匹配时找到缓存文件，必须记录下系统生成的文件名。
    1.2 技术方案
        - 文件采用系统生成的文件名，并在数据库记录文件原始名称。

3. 先不管缓存了。由于后续每次更改设置选项后，预览文件都需要后台根据这些设置条件重新生成预览图片传给前台，也就是每次预览都需要向后台获取预览文件，所以先不缓存了。

4. 目前没有在后台存储时处理同名文件问题，目前如果文件同名，只存储一个文件。

5. 获取文件页数功能尚未实现。

6. 正在修改 输入非法startPage和endPage后，输入框没有实时更新。

安装软件：Ghostscript，LibreOffice，ImageMagick，并配置环境变量。

商家角色管理，推荐单用户表 + RBAC权限模型的方案

# 支持文件类型与参数
    1. 支持文件类型
        - PDF
    2. 支持文件打印配置参数
        份数（copies）：
            - 范围：1-99份
            - 默认：1份
        纸张大小（media）：
            - A3 (297 × 420 mm)
            - A4 (210 × 297 mm)（默认）
            - A5 (148 × 210 mm)
            - B4(JIS) (257 × 364 mm)
            - B5(JIS) (182 × 257 mm)
            - Executive (184 × 267 mm, 7.25 × 10.5 in)
            - Statement (140 × 216 mm, 5.5 × 8.5 in)
            - Tabloid (279 × 432 mm, 11 × 17 in)
            - Legal (216 × 356 mm, 8.5 × 14 in)
            - Letter (216 × 279 mm, 8.5 × 11 in)
        颜色（color-mode）：
            - 黑白（mono，默认）
            - 彩色（color）
        单双面（duplex）：
            - 单面（simplex，默认）
            - 双面（duplex）
        方向（orientation）：
            - 纵向（portrait，默认）
            - 横向（landscape）
        打印范围（page-ranges）：
            - 所有页面（all，默认）
            - 指定页面（range）：
                - 起始页（from，默认为1）
                - 结束页（to，默认为文档最后一页）
                - 显示格式：第X-Y页（共Z页）
                - 页码范围验证：
                    * 起始页不能小于1
                    * 结束页不能大于总页数
                    * 结束页不能小于起始页
        多页打印（number-up）：
            - 每张纸打印的页数（pages-per-sheet）：1, 2, 4, 6, 8, 9, 16页
                - 默认：1页
                - 显示格式：每版打印X页
            - 页面布局（number-up-layout）：
                - 从左往右，再往下（lrtb，默认）
                - 从上往下，再往右（tblr）
                - 从上往下，再往左（tbrl）
                - 从右往左，再往下（rltb）
            - 页面边框（page-border）：
                - 是（true）
                - 否（false，默认）
    
# 打印机配置参数
    1. 基本信息
        打印机名称（printer-name）：
            - 格式：字符串
            - 示例：HP LaserJet Pro M404n
        
        打印机制造商（printer-make-and-model）：
            - 格式：字符串
            - 示例：HP LaserJet Pro M404n
        
        打印机序列号（printer-serial-number）：
            - 格式：字符串
            - 示例：CNCCB6HB6K
        
        打印机URI（printer-uri）：
            - 格式：URI字符串
            - 示例：ipp://*************:631/ipp/print
      
    2. 功能参数
        支持的纸张大小（media-supported）：
            - 类型：字符串数组
            - 值：[iso_a3_297x420mm, iso_a4_210x297mm, ...]
        
        支持的打印颜色（color-supported）：
            - 类型：布尔值
            - 值：true/false
        
        支持双面打印（duplex-supported）：
            - 类型：布尔值
            - 值：true/false
        
        默认分辨率（printer-resolution-default）：
            - 格式：DPI
            - 示例：600dpi
        
        支持的分辨率（printer-resolution-supported）：
            - 格式：DPI数组
            - 示例：[300dpi, 600dpi, 1200dpi]

    3. 状态参数
        打印机状态（printer-state）：
            - idle：空闲
            - processing：正在处理
            - stopped：已停止
        
        打印机详细状态（printer-state-reasons）：
            - none：正常
            - media-empty：缺纸
            - toner-low：墨粉不足
            - toner-empty：墨粉耗尽
            - cover-open：机盖打开
            - door-open：纸盒未关闭
            - media-jam：卡纸
            - offline-report：离线
            - other：其他错误

    4. 作业参数
        最大作业大小（job-k-octets-supported）：
            - 类型：整数
            - 单位：KB
            - 示例：1048576 (1GB)
        
        支持的作业优先级（job-priority-supported）：
            - 类型：整数范围
            - 范围：1-100
            - 默认：50
        
        作业保留时间（job-hold-until-supported）：
            - no-hold：立即打印
            - indefinite：无限期保留
            - day-time：白天打印
            - evening：晚上打印
            - night：夜间打印
            - weekend：周末打印
            - second-shift：第二班次打印
            - third-shift：第三班次打印

    5. 性能参数
        打印速度（pages-per-minute）：
            - 类型：整数
            - 单位：页/分钟
            - 黑白打印速度：38
            - 彩色打印速度：24

        预热时间（printer-warm-up-time）：
            - 类型：整数
            - 单位：秒
            - 示例：30

        首页打印时间（printer-first-page-out）：
            - 类型：整数
            - 单位：秒
            - 黑白首页：6.3
            - 彩色首页：7.2
            