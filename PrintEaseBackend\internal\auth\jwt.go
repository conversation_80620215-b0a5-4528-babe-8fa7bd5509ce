package auth

import (
	"PrintEaseBackend/internal/config"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v4"
)

// JWTManager JWT管理器
type JWTManager struct {
	secretKey string
	config    *config.AuthConfig
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(config config.AuthConfig) *JWTManager {
	return &JWTManager{
		secretKey: config.JWT.SecretKey,
		config:    &config,
	}
}

// GenerateToken 生成JWT token
func (m *JWTManager) GenerateToken(userID int64, username string, storeID int64, storeName string, clientType ClientType, roleType RoleType) (string, error) {
	// 获取过期时间配置
	expTime := m.GetExpirationTime(clientType, roleType)

	claims := &Claims{
		UserID:     userID,
		Username:   username,
		StoreID:    storeID,
		StoreName:  storeName,
		ClientType: clientType,
		RoleType:   roleType,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(expTime)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(m.secretKey))
}

// ValidateToken 验证并解析token
func (m *JWTManager) ValidateToken(tokenStr string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenStr, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(m.secretKey), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// GetExpirationTime 获取过期时间
func (m *JWTManager) GetExpirationTime(clientType ClientType, roleType RoleType) time.Duration {
	// 从配置中获取过期时间
	var hours int
	switch roleType {
	case RoleMerchantOwner, RoleMerchantStaff:
		switch clientType {
		case ClientTypeAgent:
			hours = m.config.JWT.ExpirationTimes.Merchant.Agent
		case ClientTypeWeb:
			hours = m.config.JWT.ExpirationTimes.Merchant.Web
		default:
			hours = 24 // 默认24小时
		}
	default:
		hours = 24
	}
	return time.Duration(hours) * time.Hour
}
