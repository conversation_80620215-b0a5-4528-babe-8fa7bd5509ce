import os
import logging
import requests
from PyQt5.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)

class FileDownloader(QObject):
    """文件下载工具，负责从后端下载文件并保存到本地"""
    
    # 定义信号
    download_started = pyqtSignal(str, str)  # 下载开始 (url, save_path)
    download_progress = pyqtSignal(str, int, int)  # 下载进度 (url, current, total)
    download_completed = pyqtSignal(str, str)  # 下载完成 (url, save_path)
    download_failed = pyqtSignal(str, str)  # 下载失败 (url, error_message)
    
    def __init__(self, api_client=None):
        """初始化文件下载器
        
        Args:
            api_client: API客户端实例，用于获取认证头信息
        """
        super().__init__()
        self.api_client = api_client
        self.chunk_size = 8192  # 下载块大小
        
    def download_file(self, url, save_path):
        """下载文件并保存到指定路径
        
        Args:
            url (str): 文件下载URL
            save_path (str): 保存路径
            
        Returns:
            bool: 下载是否成功
        """
        try:
            # 确保目标目录存在
            os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)
            
            # 准备请求头
            headers = {}
            if self.api_client:
                headers = self.api_client.get_headers()
                
            # 发送下载开始信号
            self.download_started.emit(url, save_path)
            logger.info(f"开始下载文件: {url} -> {save_path}")
            
            # 发起请求
            with requests.get(url, headers=headers, stream=True) as response:
                # 检查响应状态
                response.raise_for_status()
                
                # 获取文件大小
                total_size = int(response.headers.get('content-length', 0))
                
                # 下载文件
                downloaded_size = 0
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=self.chunk_size):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # 发送进度信号
                            self.download_progress.emit(url, downloaded_size, total_size)
                
                # 发送完成信号
                self.download_completed.emit(url, save_path)
                logger.info(f"文件下载完成: {save_path}")
                return True
                
        except requests.exceptions.RequestException as e:
            error_msg = f"下载请求失败: {str(e)}"
            logger.error(error_msg)
            self.download_failed.emit(url, error_msg)
            
            # 如果文件已部分下载，删除它
            if os.path.exists(save_path):
                try:
                    os.remove(save_path)
                    logger.info(f"已删除部分下载的文件: {save_path}")
                except Exception as del_err:
                    logger.error(f"删除部分下载的文件失败: {str(del_err)}")
            
            return False
            
        except Exception as e:
            error_msg = f"下载文件时出错: {str(e)}"
            logger.error(error_msg)
            self.download_failed.emit(url, error_msg)
            
            # 如果文件已部分下载，删除它
            if os.path.exists(save_path):
                try:
                    os.remove(save_path)
                    logger.info(f"已删除部分下载的文件: {save_path}")
                except Exception as del_err:
                    logger.error(f"删除部分下载的文件失败: {str(del_err)}")
            
            return False 